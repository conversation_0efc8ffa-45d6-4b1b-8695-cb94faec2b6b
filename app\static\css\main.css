/* 聊天平台主样式文件 */

/* CSS变量定义 */
:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --sidebar-width: 320px;
    --header-height: 60px;
    --input-height: 80px;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
}

/* 加载屏幕 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 屏幕切换 */
.screen {
    display: none;
    height: 100vh;
}

.screen.active {
    display: flex;
}

/* 登录界面 */
#login-screen {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    padding: 40px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.login-header p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* 标签页 */
.login-tabs {
    display: flex;
    margin-bottom: 30px;
    border-radius: var(--radius-md);
    background: var(--background-color);
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: var(--surface-color);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.tab-btn:hover:not(.active) {
    color: var(--text-primary);
}

/* 表单 */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color 0.2s ease;
    background: var(--surface-color);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group input::placeholder {
    color: var(--text-muted);
}

/* 按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    width: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.1rem;
}

.btn-icon:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

/* 消息提示 */
.message {
    margin-top: 20px;
    padding: 12px 16px;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    text-align: center;
    display: none;
}

.message.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.message.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.message.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

/* 聊天界面布局 */
#chat-screen {
    flex-direction: row;
}

.sidebar {
    width: var(--sidebar-width);
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    min-width: 0;
}

/* 侧边栏头部 */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-details {
    min-width: 0;
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    font-size: 0.8rem;
    color: var(--success-color);
}

.sidebar-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

/* 侧边栏导航 */
.sidebar-nav {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.nav-btn {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
}

.nav-btn:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.nav-btn.active {
    background: var(--primary-color);
    color: white;
}

/* 侧边栏内容 */
.sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: none;
}

.sidebar-content.active {
    display: block;
}

.content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.content-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 聊天头部 */
.chat-header {
    height: var(--header-height);
    padding: 0 24px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.room-info h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.room-members {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.chat-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
}

/* 消息容器 */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
    background: var(--background-color);
}

.welcome-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.welcome-message h3 {
    font-size: 1.5rem;
    margin-bottom: 12px;
    color: var(--text-primary);
}

/* 输入区域 */
.input-area {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 16px 24px;
    flex-shrink: 0;
}

.input-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

#message-input {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 10px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-family: inherit;
    resize: none;
    transition: border-color 0.2s ease;
}

#message-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.send-btn {
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
}

/* 房间列表 */
.rooms-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.room-item {
    padding: 16px;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--surface-color);
}

.room-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.room-item.active {
    border-color: var(--primary-color);
    background: #f0f9ff;
}

.room-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.room-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.room-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.room-members-count::before {
    content: "👥 ";
}

.room-last-activity::before {
    content: "🕒 ";
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
