from flask_socketio import emit, join_room, leave_room, disconnect
from flask import request
from app import socketio, db
from app.models.user import User, AnonymousUser
from app.models.room import Room, RoomMember
from app.models.message import Message
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 存储用户连接信息
connected_users = {}

def get_user_from_auth(auth_data):
    """从认证数据获取用户"""
    if not auth_data:
        return None, None
    
    if 'token' in auth_data:
        # JWT用户
        try:
            from flask_jwt_extended import decode_token
            decoded = decode_token(auth_data['token'])
            user_id = decoded['sub']
            user = User.query.get(user_id)
            if user and user.is_active:
                return user, 'user'
        except:
            pass
    
    if 'session_id' in auth_data:
        # 匿名用户
        anonymous_user = AnonymousUser.query.filter_by(session_id=auth_data['session_id']).first()
        if anonymous_user:
            return anonymous_user, 'anonymous'
    
    return None, None

@socketio.on('connect')
def handle_connect(auth):
    """处理WebSocket连接"""
    user, user_type = get_user_from_auth(auth)
    
    if not user:
        logger.warning(f"Unauthorized connection attempt from {request.remote_addr}")
        disconnect()
        return False
    
    # 记录连接信息
    connected_users[request.sid] = {
        'user_id': user.id,
        'user_type': user_type,
        'user_name': user.username if user_type == 'user' else (user.nickname or f'匿名用户{user.id[:8]}'),
        'connected_at': datetime.utcnow(),
        'rooms': set()
    }
    
    logger.info(f"User {user.id} ({user_type}) connected with session {request.sid}")
    
    emit('connected', {
        'user_id': user.id,
        'user_type': user_type,
        'message': '连接成功'
    })

@socketio.on('disconnect')
def handle_disconnect():
    """处理WebSocket断开连接"""
    if request.sid in connected_users:
        user_info = connected_users[request.sid]
        user_id = user_info['user_id']
        user_type = user_info['user_type']
        
        # 更新所有房间的在线状态
        for room_id in user_info['rooms']:
            update_room_member_status(room_id, user_id, user_type, False)
            
            # 通知房间其他成员
            emit('user_left', {
                'user_id': user_id,
                'user_name': user_info['user_name'],
                'user_type': user_type
            }, room=room_id)
        
        # 移除连接记录
        del connected_users[request.sid]
        
        logger.info(f"User {user_id} ({user_type}) disconnected")

@socketio.on('join_room')
def handle_join_room(data):
    """处理加入房间"""
    if request.sid not in connected_users:
        emit('error', {'message': '未认证的连接'})
        return
    
    room_id = data.get('room_id')
    if not room_id:
        emit('error', {'message': '缺少房间ID'})
        return
    
    user_info = connected_users[request.sid]
    user_id = user_info['user_id']
    user_type = user_info['user_type']
    
    # 验证用户是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user_id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user_id, is_active=True
        ).first()
    
    if not member:
        emit('error', {'message': '您不是房间成员'})
        return
    
    # 加入Socket.IO房间
    join_room(room_id)
    user_info['rooms'].add(room_id)
    
    # 更新在线状态
    update_room_member_status(room_id, user_id, user_type, True)
    
    # 获取房间信息
    room = Room.query.get(room_id)
    if room:
        # 获取在线成员数
        online_count = get_room_online_count(room_id)
        
        # 通知用户加入成功
        emit('room_joined', {
            'room_id': room_id,
            'room_name': room.name,
            'online_count': online_count
        })
        
        # 通知房间其他成员
        emit('user_joined', {
            'user_id': user_id,
            'user_name': user_info['user_name'],
            'user_type': user_type,
            'online_count': online_count
        }, room=room_id, include_self=False)
        
        logger.info(f"User {user_id} joined room {room_id}")

@socketio.on('leave_room')
def handle_leave_room(data):
    """处理离开房间"""
    if request.sid not in connected_users:
        return
    
    room_id = data.get('room_id')
    if not room_id:
        return
    
    user_info = connected_users[request.sid]
    user_id = user_info['user_id']
    user_type = user_info['user_type']
    
    if room_id in user_info['rooms']:
        # 离开Socket.IO房间
        leave_room(room_id)
        user_info['rooms'].remove(room_id)
        
        # 更新在线状态
        update_room_member_status(room_id, user_id, user_type, False)
        
        # 获取在线成员数
        online_count = get_room_online_count(room_id)
        
        # 通知房间其他成员
        emit('user_left', {
            'user_id': user_id,
            'user_name': user_info['user_name'],
            'user_type': user_type,
            'online_count': online_count
        }, room=room_id)
        
        logger.info(f"User {user_id} left room {room_id}")

@socketio.on('send_message')
def handle_send_message(data):
    """处理发送消息（通过WebSocket）"""
    if request.sid not in connected_users:
        emit('error', {'message': '未认证的连接'})
        return
    
    user_info = connected_users[request.sid]
    user_id = user_info['user_id']
    user_type = user_info['user_type']
    
    room_id = data.get('room_id')
    content = data.get('content', '').strip()
    message_type = data.get('message_type', 'text')
    reply_to_id = data.get('reply_to_id')
    
    if not room_id or not content:
        emit('error', {'message': '缺少必要参数'})
        return
    
    if room_id not in user_info['rooms']:
        emit('error', {'message': '您不在该房间中'})
        return
    
    try:
        # 创建消息
        message = Message(
            room_id=room_id,
            user_id=user_id if user_type == 'user' else None,
            anonymous_user_id=user_id if user_type == 'anonymous' else None,
            content=content,
            message_type=message_type,
            reply_to_id=reply_to_id
        )
        
        db.session.add(message)
        db.session.commit()
        
        # 广播消息到房间
        emit('new_message', {
            'message': message.to_dict()
        }, room=room_id)
        
        logger.info(f"Message sent by {user_id} in room {room_id}")
        
    except Exception as e:
        db.session.rollback()
        emit('error', {'message': f'发送消息失败: {str(e)}'})
        logger.error(f"Failed to send message: {e}")

@socketio.on('typing')
def handle_typing(data):
    """处理正在输入状态"""
    if request.sid not in connected_users:
        return
    
    user_info = connected_users[request.sid]
    room_id = data.get('room_id')
    is_typing = data.get('is_typing', False)
    
    if room_id and room_id in user_info['rooms']:
        emit('user_typing', {
            'user_id': user_info['user_id'],
            'user_name': user_info['user_name'],
            'is_typing': is_typing
        }, room=room_id, include_self=False)

def update_room_member_status(room_id, user_id, user_type, is_online):
    """更新房间成员在线状态"""
    try:
        member = None
        if user_type == 'user':
            member = RoomMember.query.filter_by(
                room_id=room_id, user_id=user_id, is_active=True
            ).first()
        else:
            member = RoomMember.query.filter_by(
                room_id=room_id, anonymous_user_id=user_id, is_active=True
            ).first()
        
        if member:
            member.is_online = is_online
            member.last_seen = datetime.utcnow()
            db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger.error(f"Failed to update member status: {e}")

def get_room_online_count(room_id):
    """获取房间在线人数"""
    try:
        return RoomMember.query.filter_by(
            room_id=room_id, is_active=True, is_online=True
        ).count()
    except:
        return 0

@socketio.on('ping')
def handle_ping():
    """处理心跳包"""
    emit('pong')

@socketio.on_error_default
def default_error_handler(e):
    """默认错误处理"""
    logger.error(f"WebSocket error: {e}")
    emit('error', {'message': '服务器内部错误'})
