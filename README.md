# 聊天平台 - Chat Platform

一个功能完整的现代化聊天平台，支持实时通信、房间管理、文件上传、人物信息系统和问卷功能。

## 🚀 功能特性

### 核心功能
- ✅ **用户系统**: 支持注册用户和匿名用户
- ✅ **房间管理**: 创建房间、密码保护、邀请链接
- ✅ **实时聊天**: WebSocket实时通信、消息撤回、回复功能
- ✅ **文件上传**: 图片压缩上传、语音消息
- ✅ **人物信息**: 可配置的用户信息分类、隐藏/揭露机制
- ✅ **问卷系统**: 创建问卷、房间内发送、统计回答
- ✅ **管理后台**: 用户管理、邀请码、聊天记录导出

### 技术特性
- 🔒 JWT认证 + 会话认证
- 📱 响应式设计，支持PC和移动端
- 🗄️ SQLite数据库，轻量级部署
- ⚡ 基于性能的自适应并发控制
- 🔄 实时在线状态管理
- 📊 完整的管理员日志系统

## 📋 系统要求

- Python 3.8+
- SQLite 3
- 现代浏览器（支持WebSocket）

## 🛠️ 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd chat-platform
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境
复制并编辑配置文件：
```bash
cp config/config.py.example config/config.py
```

主要配置项：
- `SECRET_KEY`: Flask密钥
- `JWT_SECRET_KEY`: JWT密钥
- `ADMIN_USERNAME`: 默认管理员用户名
- `ADMIN_PASSWORD`: 默认管理员密码
- `UPLOAD_FOLDER`: 文件上传目录

### 4. 启动服务
```bash
python run.py
```

服务将在 `http://localhost:5000` 启动

## 📚 API 文档

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/anonymous` - 创建匿名用户
- `POST /api/auth/switch-to-user` - 匿名用户转注册用户

### 房间管理
- `GET /api/rooms/` - 获取房间列表
- `POST /api/rooms/` - 创建房间
- `POST /api/rooms/{id}/join` - 加入房间
- `POST /api/rooms/{id}/leave` - 离开房间
- `GET /api/rooms/{id}/members` - 获取房间成员
- `POST /api/rooms/{id}/invite-link` - 创建邀请链接

### 消息系统
- `GET /api/messages/{room_id}` - 获取消息历史
- `POST /api/messages/{room_id}` - 发送消息
- `POST /api/messages/{id}/recall` - 撤回消息
- `POST /api/messages/{id}/react` - 消息反应

### 文件上传
- `POST /api/files/upload/image/{room_id}` - 上传图片
- `POST /api/files/upload/audio/{room_id}` - 上传语音
- `GET /api/files/download/{path}` - 下载文件

### 人物信息
- `GET /api/profiles/categories` - 获取信息分类
- `GET /api/profiles/my-profile` - 获取我的信息
- `POST /api/profiles/my-profile` - 更新我的信息
- `POST /api/profiles/reveal/{room_id}` - 在房间中揭露信息

### 问卷系统
- `GET /api/questionnaires/` - 获取问卷列表
- `POST /api/questionnaires/` - 创建问卷
- `POST /api/questionnaires/{id}/send/{room_id}` - 发送问卷
- `POST /api/questionnaires/respond/{instance_id}` - 回答问卷

### 管理员功能
- `GET /api/admin/dashboard` - 管理员仪表板
- `GET /api/admin/users` - 用户管理
- `POST /api/admin/invite-codes` - 创建邀请码
- `GET /api/admin/rooms/{id}/messages/export` - 导出聊天记录

## 🔌 WebSocket 事件

### 客户端发送
- `connect` - 连接到服务器
- `join_room` - 加入房间
- `leave_room` - 离开房间
- `send_message` - 发送消息
- `typing` - 正在输入状态

### 服务器推送
- `connected` - 连接成功
- `new_message` - 新消息
- `message_recalled` - 消息撤回
- `user_joined` - 用户加入房间
- `user_left` - 用户离开房间
- `user_typing` - 用户正在输入

## 🗂️ 项目结构

```
chat-platform/
├── app/
│   ├── __init__.py          # 应用工厂
│   ├── models/              # 数据模型
│   │   ├── user.py         # 用户模型
│   │   ├── room.py         # 房间模型
│   │   ├── message.py      # 消息模型
│   │   ├── profile.py      # 人物信息模型
│   │   ├── questionnaire.py # 问卷模型
│   │   └── admin.py        # 管理员日志模型
│   ├── routes/              # 路由处理
│   │   ├── auth.py         # 认证路由
│   │   ├── rooms.py        # 房间路由
│   │   ├── messages.py     # 消息路由
│   │   ├── files.py        # 文件路由
│   │   ├── profiles.py     # 人物信息路由
│   │   ├── questionnaires.py # 问卷路由
│   │   ├── admin.py        # 管理员路由
│   │   └── websocket_events.py # WebSocket事件
│   └── utils/
│       └── helpers.py      # 工具函数
├── config/
│   └── config.py          # 配置文件
├── templates/
│   └── index.html         # 测试页面
├── requirements.txt       # 依赖列表
├── run.py                # 启动文件
└── README.md             # 说明文档
```

## 🔧 开发指南

### 数据库模型
系统使用SQLAlchemy ORM，主要模型包括：
- `User` - 注册用户
- `AnonymousUser` - 匿名用户
- `Room` - 聊天房间
- `Message` - 消息
- `ProfileCategory` - 人物信息分类
- `Questionnaire` - 问卷

### 认证机制
- 注册用户使用JWT Token认证
- 匿名用户使用Session ID认证
- 支持用户类型切换

### 文件上传
- 图片自动压缩
- 支持多种格式
- 安全文件名处理
- 大小限制控制

## 🚀 部署建议

### 生产环境
1. 使用Gunicorn + Nginx
2. 配置HTTPS
3. 使用Redis作为Session存储
4. 配置文件上传CDN
5. 启用日志轮转

### 性能优化
1. 数据库连接池
2. 静态文件CDN
3. WebSocket连接管理
4. 消息队列处理

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请创建Issue或联系开发团队。
