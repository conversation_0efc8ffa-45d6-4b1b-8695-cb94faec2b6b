from flask import Blueprint, render_template, send_from_directory, current_app, jsonify
import os

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    """主页"""
    return render_template('index.html')

@bp.route('/room/<room_id>')
def room(room_id):
    """房间页面"""
    return render_template('room.html', room_id=room_id)

@bp.route('/admin')
def admin():
    """管理员页面"""
    return render_template('admin.html')

@bp.route('/join/<token>')
def join_room(token):
    """通过邀请链接加入房间"""
    return render_template('join.html', token=token)

@bp.route('/uploads/<path:filename>')
def uploaded_file(filename):
    """提供上传文件的访问"""
    return send_from_directory(current_app.config['UPLOAD_FOLDER'], filename)

@bp.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('app/static', filename)

@bp.route('/api')
def api_info():
    """API信息"""
    return jsonify({
        'name': '聊天平台 API',
        'version': '1.0.0',
        'description': '支持实时聊天、房间管理、文件上传、人物信息、问卷系统的综合聊天平台',
        'endpoints': {
            'auth': '/api/auth/*',
            'rooms': '/api/rooms/*',
            'messages': '/api/messages/*',
            'files': '/api/files/*',
            'profiles': '/api/profiles/*',
            'questionnaires': '/api/questionnaires/*',
            'admin': '/api/admin/*'
        },
        'websocket': {
            'url': '/socket.io/',
            'events': ['connect', 'join_room', 'leave_room', 'send_message', 'typing']
        }
    })
