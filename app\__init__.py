from flask import Flask
from flask_socketio import Socket<PERSON>
from flask_jwt_extended import J<PERSON>TManager
from flask_cors import CORS
from config.config import Config
from app.models import db
import os

socketio = SocketIO()
jwt = JWTManager()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, origins="*", supports_credentials=True)
    socketio.init_app(app, cors_allowed_origins="*", async_mode='threading')
    
    # 创建上传目录
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'images'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'audio'), exist_ok=True)
    
    # 注册蓝图
    from app.routes.auth import bp as auth_bp
    from app.routes.rooms import bp as rooms_bp
    from app.routes.messages import bp as messages_bp
    from app.routes.files import bp as files_bp
    from app.routes.profiles import bp as profiles_bp
    from app.routes.questionnaires import bp as questionnaires_bp
    from app.routes.admin import bp as admin_bp
    from app.routes.main import bp as main_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(rooms_bp, url_prefix='/api/rooms')
    app.register_blueprint(messages_bp, url_prefix='/api/messages')
    app.register_blueprint(files_bp, url_prefix='/api/files')
    app.register_blueprint(profiles_bp, url_prefix='/api/profiles')
    app.register_blueprint(questionnaires_bp, url_prefix='/api/questionnaires')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(main_bp)
    
    # 注册WebSocket事件
    from app.routes import websocket_events
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        
        # 创建默认管理员用户
        from app.models.user import User
        admin = User.query.filter_by(username=app.config['ADMIN_USERNAME']).first()
        if not admin:
            admin = User(
                username=app.config['ADMIN_USERNAME'],
                is_admin=True,
                can_create_room=True,
                is_active=True
            )
            admin.set_password(app.config['ADMIN_PASSWORD'])
            db.session.add(admin)
            db.session.commit()
            print(f"Created default admin user: {app.config['ADMIN_USERNAME']}")
        
        # 创建默认人物信息分类
        from app.models.profile import ProfileCategory, ProfileField
        categories_data = [
            {
                'name': 'occupation',
                'display_name': '职业身份',
                'description': '用户的职业和身份信息',
                'fields': [
                    {'name': 'job', 'display_name': '职业', 'field_type': 'single_choice', 
                     'options': ['学生', '教师', '工程师', '医生', '律师', '设计师', '销售', '自由职业', '其他']},
                    {'name': 'industry', 'display_name': '行业', 'field_type': 'single_choice',
                     'options': ['互联网', '教育', '医疗', '金融', '制造业', '服务业', '政府机关', '其他']}
                ]
            },
            {
                'name': 'hobbies',
                'display_name': '兴趣爱好',
                'description': '用户的兴趣爱好',
                'fields': [
                    {'name': 'interests', 'display_name': '兴趣爱好', 'field_type': 'multiple_choice', 'max_selections': 5,
                     'options': ['阅读', '音乐', '电影', '运动', '旅行', '摄影', '绘画', '编程', '游戏', '美食', '宠物', '其他']}
                ]
            },
            {
                'name': 'personal',
                'display_name': '个人信息',
                'description': '用户的基本个人信息',
                'fields': [
                    {'name': 'age_range', 'display_name': '年龄段', 'field_type': 'single_choice',
                     'options': ['18岁以下', '18-25岁', '26-30岁', '31-35岁', '36-40岁', '40岁以上']},
                    {'name': 'personality', 'display_name': '性格', 'field_type': 'multiple_choice', 'max_selections': 3,
                     'options': ['外向', '内向', '乐观', '悲观', '理性', '感性', '冒险', '保守', '幽默', '严肃']}
                ]
            }
        ]
        
        for cat_data in categories_data:
            category = ProfileCategory.query.filter_by(name=cat_data['name']).first()
            if not category:
                category = ProfileCategory(
                    name=cat_data['name'],
                    display_name=cat_data['display_name'],
                    description=cat_data['description']
                )
                db.session.add(category)
                db.session.flush()  # 获取category.id
                
                for field_data in cat_data['fields']:
                    field = ProfileField(
                        category_id=category.id,
                        name=field_data['name'],
                        display_name=field_data['display_name'],
                        field_type=field_data['field_type'],
                        max_selections=field_data.get('max_selections')
                    )
                    field.set_options(field_data['options'])
                    db.session.add(field)
        
        db.session.commit()
        print("Database initialized successfully")
    
    return app
