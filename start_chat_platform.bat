@echo off
chcp 65001 >nul
title 聊天平台 - 一键启动

echo.
echo ========================================
echo           聊天平台一键启动
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到Python，请先安装Python 3.8+
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测通过
echo.

:: 检查是否存在虚拟环境
if not exist "venv" (
    echo 🔧 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
) else (
    echo ✅ 虚拟环境已存在
)

echo.
echo 🚀 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo ✅ 虚拟环境激活成功
echo.

:: 升级pip
echo 📦 升级pip到最新版本...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠️  pip升级失败，但继续安装依赖...
) else (
    echo ✅ pip升级成功
)

echo.

:: 检查requirements.txt是否存在
if not exist "requirements.txt" (
    echo ❌ 错误: 未找到requirements.txt文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 安装依赖
echo 📦 安装项目依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    echo 请检查网络连接或requirements.txt文件
    pause
    exit /b 1
)

echo ✅ 依赖安装成功
echo.

:: 检查主要文件是否存在
if not exist "run.py" (
    echo ❌ 错误: 未找到run.py启动文件
    pause
    exit /b 1
)

if not exist "app" (
    echo ❌ 错误: 未找到app目录
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过
echo.

:: 创建必要的目录
if not exist "uploads" mkdir uploads
if not exist "uploads\images" mkdir uploads\images
if not exist "uploads\audio" mkdir uploads\audio
if not exist "instance" mkdir instance

echo ✅ 目录结构检查完成
echo.

:: 显示启动信息
echo ========================================
echo           🚀 启动聊天平台
echo ========================================
echo.
echo 📍 访问地址: http://localhost:5000
echo 📊 API测试页面: http://localhost:5000
echo 📋 API信息: http://localhost:5000/api
echo ⚙️  默认管理员: admin / admin123
echo.
echo 💡 提示:
echo   - 按 Ctrl+C 停止服务器
echo   - 服务器启动后会自动打开浏览器
echo   - 首次启动会自动初始化数据库
echo.
echo ========================================
echo.

:: 启动服务器
echo 🔥 正在启动服务器...
echo.

:: 延迟3秒后打开浏览器
start /b timeout /t 3 /nobreak >nul && start http://localhost:5000

:: 启动Python应用
python run.py

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 服务器启动失败
    echo 请检查错误信息并重试
    echo.
    pause
)

echo.
echo 👋 聊天平台已停止运行
pause
