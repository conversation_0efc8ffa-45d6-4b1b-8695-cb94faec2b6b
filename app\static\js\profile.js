// 个人信息管理

class ProfileManager {
    constructor() {
        this.profile = null;
        this.categories = [];
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听侧边栏视图切换
        EventBus.on('sidebar:viewChanged', (view) => {
            if (view === 'profile') {
                this.loadProfile();
            }
        });
    }
    
    // 加载个人信息
    async loadProfile() {
        try {
            // 加载信息分类
            const categoriesResponse = await ProfileAPI.getCategories();
            this.categories = categoriesResponse.categories || [];
            
            // 加载个人信息
            const profileResponse = await ProfileAPI.getMyProfile();
            this.profile = profileResponse.profile || {};
            
            this.renderProfile();
            
        } catch (error) {
            console.error('加载个人信息失败:', error);
            showNotification('加载个人信息失败', 'error');
        }
    }
    
    // 渲染个人信息
    renderProfile() {
        const profileContent = DOM.$('#profile-content');
        if (!profileContent) return;
        
        if (this.categories.length === 0) {
            profileContent.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">👤</div>
                    <div class="empty-state-title">暂无信息分类</div>
                    <div class="empty-state-description">请联系管理员设置信息分类</div>
                </div>
            `;
            return;
        }
        
        const categoriesHTML = this.categories.map(category => {
            const categoryInfo = this.profile[category.id] || {};
            return `
                <div class="profile-category">
                    <div class="category-header">
                        <h4>${StringUtils.escapeHtml(category.name)}</h4>
                        <button class="btn-icon" onclick="window.profileManager.editCategory('${category.id}')" title="编辑">✏️</button>
                    </div>
                    <div class="category-description">
                        ${StringUtils.escapeHtml(category.description || '')}
                    </div>
                    <div class="category-info">
                        ${this.renderCategoryInfo(category, categoryInfo)}
                    </div>
                </div>
            `;
        }).join('');
        
        profileContent.innerHTML = categoriesHTML;
    }
    
    // 渲染分类信息
    renderCategoryInfo(category, info) {
        if (!info.content) {
            return `
                <div class="info-empty">
                    <span>暂未填写</span>
                    <button class="btn btn-secondary btn-sm" onclick="window.profileManager.editCategory('${category.id}')">
                        添加信息
                    </button>
                </div>
            `;
        }
        
        return `
            <div class="info-content">
                <div class="info-text">${StringUtils.escapeHtml(info.content)}</div>
                <div class="info-meta">
                    <span class="info-visibility">
                        ${info.is_hidden ? '🔒 隐藏' : '👁️ 公开'}
                    </span>
                    <span class="info-updated">
                        更新于 ${TimeUtils.formatDateTime(info.updated_at)}
                    </span>
                </div>
            </div>
        `;
    }
    
    // 编辑分类信息
    editCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (!category) return;
        
        const info = this.profile[categoryId] || {};
        
        const content = `
            <div class="form-group">
                <label class="form-label">${StringUtils.escapeHtml(category.name)}</label>
                <textarea id="category-content" class="form-textarea" placeholder="输入${category.name}信息">${StringUtils.escapeHtml(info.content || '')}</textarea>
            </div>
            <div class="form-group">
                <div class="form-checkbox">
                    <input type="checkbox" id="category-hidden" ${info.is_hidden ? 'checked' : ''}>
                    <label for="category-hidden">设为隐藏（需要在房间中主动揭露）</label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="window.profileManager.saveCategory('${categoryId}')">保存</button>
            </div>
        `;
        
        window.app.showModal(content, `编辑${category.name}`);
    }
    
    // 保存分类信息
    async saveCategory(categoryId) {
        const content = DOM.$('#category-content').value.trim();
        const isHidden = DOM.$('#category-hidden').checked;
        
        if (!content) {
            showNotification('请输入信息内容', 'error');
            return;
        }
        
        try {
            const profileData = {
                [categoryId]: {
                    content,
                    is_hidden: isHidden
                }
            };
            
            await ProfileAPI.updateMyProfile(profileData);
            
            window.app.closeModal();
            showNotification('信息已保存', 'success');
            
            // 重新加载个人信息
            await this.loadProfile();
            
        } catch (error) {
            console.error('保存信息失败:', error);
            showNotification('保存信息失败', 'error');
        }
    }
    
    // 在房间中揭露信息
    async revealInfo(categoryId, infoId) {
        if (!AppState.currentRoom) {
            showNotification('请先加入一个房间', 'error');
            return;
        }
        
        try {
            await ProfileAPI.revealInfo(AppState.currentRoom.id, categoryId, infoId);
            showNotification('信息已揭露', 'success');
            
        } catch (error) {
            console.error('揭露信息失败:', error);
            showNotification('揭露信息失败', 'error');
        }
    }
    
    // 查看用户信息
    async viewUserProfile(userId) {
        if (!AppState.currentRoom) {
            showNotification('请先加入一个房间', 'error');
            return;
        }
        
        try {
            const response = await ProfileAPI.getUserProfile(userId, AppState.currentRoom.id);
            const userProfile = response.profile || {};
            
            this.showUserProfileModal(userProfile);
            
        } catch (error) {
            console.error('获取用户信息失败:', error);
            showNotification('获取用户信息失败', 'error');
        }
    }
    
    // 显示用户信息模态框
    showUserProfileModal(userProfile) {
        const profileHTML = this.categories.map(category => {
            const categoryInfo = userProfile[category.id];
            if (!categoryInfo || categoryInfo.is_hidden) {
                return `
                    <div class="profile-category">
                        <div class="category-header">
                            <h4>${StringUtils.escapeHtml(category.name)}</h4>
                        </div>
                        <div class="info-empty">
                            <span>信息未公开</span>
                        </div>
                    </div>
                `;
            }
            
            return `
                <div class="profile-category">
                    <div class="category-header">
                        <h4>${StringUtils.escapeHtml(category.name)}</h4>
                    </div>
                    <div class="info-content">
                        <div class="info-text">${StringUtils.escapeHtml(categoryInfo.content)}</div>
                    </div>
                </div>
            `;
        }).join('');
        
        const content = `
            <div class="user-profile-modal">
                ${profileHTML}
            </div>
        `;
        
        window.app.showModal(content, `${userProfile.username || '用户'}的信息`);
    }
}

// 全局函数
window.editProfile = function() {
    if (window.profileManager) {
        showNotification('请在下方分类中编辑具体信息', 'info');
    }
};

// 初始化个人信息管理器
document.addEventListener('DOMContentLoaded', () => {
    window.profileManager = new ProfileManager();
});
