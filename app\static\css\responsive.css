/* 响应式设计文件 */

/* 平板设备 */
@media (max-width: 1024px) {
    :root {
        --sidebar-width: 280px;
    }
    
    .login-container {
        padding: 30px;
        max-width: 380px;
    }
    
    .message-bubble {
        max-width: 80%;
    }
    
    .modal {
        max-width: 95vw;
        margin: 20px;
    }
    
    .notification {
        max-width: 350px;
        right: 16px;
        top: 16px;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
        --header-height: 56px;
        --input-height: 70px;
    }
    
    body {
        overflow: auto;
    }
    
    /* 登录界面移动端适配 */
    #login-screen {
        padding: 16px;
    }
    
    .login-container {
        padding: 24px;
        max-width: 100%;
        box-shadow: none;
        border-radius: var(--radius-md);
    }
    
    .login-header h1 {
        font-size: 1.75rem;
    }
    
    .tab-btn {
        padding: 8px 12px;
        font-size: 0.85rem;
    }
    
    /* 聊天界面移动端布局 */
    #chat-screen {
        position: relative;
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 100;
        transition: left 0.3s ease;
        box-shadow: var(--shadow-lg);
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .main-content {
        width: 100%;
    }
    
    .mobile-menu-toggle {
        display: flex !important;
    }
    
    /* 聊天头部移动端 */
    .chat-header {
        padding: 0 16px;
        height: var(--header-height);
    }
    
    .room-info h2 {
        font-size: 1.1rem;
    }
    
    .room-members {
        font-size: 0.8rem;
    }
    
    /* 消息容器移动端 */
    .messages-container {
        padding: 16px;
        height: calc(100vh - var(--header-height) - var(--input-height));
    }
    
    .message-bubble {
        max-width: 85%;
    }
    
    .message-content {
        padding: 10px 14px;
    }
    
    .message-header {
        margin-bottom: 4px;
    }
    
    .message-avatar {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }
    
    .message-sender {
        font-size: 0.8rem;
    }
    
    .message-time {
        font-size: 0.7rem;
    }
    
    /* 输入区域移动端 */
    .input-area {
        padding: 12px 16px;
        height: var(--input-height);
    }
    
    .input-actions {
        margin-bottom: 8px;
    }
    
    #message-input {
        font-size: 16px; /* 防止iOS缩放 */
        min-height: 36px;
        padding: 8px 14px;
    }
    
    .send-btn {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }
    
    /* 侧边栏移动端 */
    .sidebar-header {
        padding: 16px;
    }
    
    .user-avatar {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }
    
    .user-name {
        font-size: 0.9rem;
    }
    
    .sidebar-nav {
        padding: 12px 16px;
    }
    
    .nav-btn {
        padding: 10px 14px;
        font-size: 0.85rem;
    }
    
    .sidebar-content {
        padding: 16px;
    }
    
    .content-header h3 {
        font-size: 1rem;
    }
    
    /* 房间列表移动端 */
    .room-item {
        padding: 14px;
    }
    
    .room-name {
        font-size: 0.95rem;
    }
    
    .room-description {
        font-size: 0.8rem;
    }
    
    .room-meta {
        font-size: 0.75rem;
    }
    
    /* 模态框移动端 */
    .modal {
        max-width: 100vw;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
    }
    
    .modal-header {
        padding: 16px 20px;
    }
    
    .modal-title {
        font-size: 1.1rem;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        padding: 12px 20px;
        flex-direction: column-reverse;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .modal-footer .btn:last-child {
        margin-bottom: 0;
    }
    
    /* 通知移动端 */
    .notification {
        position: fixed;
        top: 16px;
        left: 16px;
        right: 16px;
        max-width: none;
        animation: notificationSlideDown 0.3s ease-out;
    }
    
    @keyframes notificationSlideDown {
        from {
            opacity: 0;
            transform: translateY(-100%);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* 表单移动端 */
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-row .form-group {
        margin-bottom: 20px;
    }
    
    .form-group input,
    .form-select,
    .form-textarea {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 10px 14px;
    }
    
    /* 文件消息移动端 */
    .message-file {
        padding: 10px;
        gap: 10px;
    }
    
    .file-icon {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }
    
    .file-name {
        font-size: 0.9rem;
    }
    
    .file-size {
        font-size: 0.75rem;
    }
    
    .file-download {
        padding: 4px 8px;
        font-size: 0.75rem;
    }
    
    /* 图片消息移动端 */
    .message-image {
        max-width: 250px;
    }
    
    /* 语音消息移动端 */
    .message-audio {
        padding: 10px;
        gap: 10px;
        min-width: 180px;
    }
    
    .audio-play-btn {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
    
    .audio-waveform {
        height: 26px;
    }
    
    .audio-duration {
        font-size: 0.75rem;
    }
    
    /* 系统消息移动端 */
    .system-message {
        margin: 16px 0;
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    /* 正在输入指示器移动端 */
    .typing-indicator {
        padding: 8px 12px;
        margin-bottom: 12px;
        max-width: 160px;
    }
    
    .typing-avatar {
        width: 20px;
        height: 20px;
        font-size: 0.6rem;
    }
    
    /* 欢迎消息移动端 */
    .welcome-message {
        padding: 40px 16px;
    }
    
    .welcome-message h3 {
        font-size: 1.3rem;
    }
    
    /* 空状态移动端 */
    .empty-state {
        padding: 30px 16px;
    }
    
    .empty-state-icon {
        font-size: 2.5rem;
        margin-bottom: 12px;
    }
    
    .empty-state-title {
        font-size: 1.1rem;
    }
    
    .empty-state-description {
        font-size: 0.85rem;
    }
}

/* 小屏手机 */
@media (max-width: 480px) {
    .login-container {
        padding: 20px;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .tab-btn {
        padding: 6px 8px;
        font-size: 0.8rem;
    }
    
    .message-bubble {
        max-width: 90%;
    }
    
    .message-content {
        padding: 8px 12px;
    }
    
    .input-area {
        padding: 10px 12px;
    }
    
    .messages-container {
        padding: 12px;
    }
    
    .chat-header {
        padding: 0 12px;
    }
    
    .sidebar-header,
    .sidebar-nav,
    .sidebar-content {
        padding-left: 12px;
        padding-right: 12px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
}

/* 横屏模式 */
@media (max-width: 768px) and (orientation: landscape) {
    .messages-container {
        height: calc(100vh - var(--header-height) - var(--input-height) - env(keyboard-inset-height, 0px));
    }
    
    .input-area {
        padding-bottom: calc(12px + env(keyboard-inset-height, 0px));
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn-icon {
        width: 44px;
        height: 44px;
    }
    
    .message-action-btn {
        padding: 6px 10px;
        min-height: 32px;
    }
    
    .tab-btn {
        min-height: 44px;
    }
    
    .nav-btn {
        min-height: 44px;
    }
    
    .room-item {
        min-height: 60px;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
        --text-muted: #333333;
    }
    
    .message-content {
        border-width: 2px;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 4px solid var(--primary-color);
    }
}
