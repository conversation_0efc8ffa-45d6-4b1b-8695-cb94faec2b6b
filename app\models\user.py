from . import db, generate_uuid
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_jwt_extended import create_access_token
import uuid

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    is_admin = db.Column(db.<PERSON>, default=False)
    can_create_room = db.Column(db.Bo<PERSON>an, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # 关系
    created_rooms = db.relationship('Room', backref='creator', lazy='dynamic', foreign_keys='Room.creator_id')
    room_memberships = db.relationship('RoomMember', backref='user', lazy='dynamic')
    messages = db.relationship('Message', backref='user', lazy='dynamic', foreign_keys='Message.user_id')
    profiles = db.relationship('UserProfile', backref='user', lazy='dynamic')
    questionnaire_responses = db.relationship('QuestionnaireResponse', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def generate_token(self):
        return create_access_token(identity=self.id)
    
    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'username': self.username,
            'is_admin': self.is_admin,
            'can_create_room': self.can_create_room,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'is_active': self.is_active
        }
        if include_sensitive:
            data['email'] = self.email
        return data

class AnonymousUser(db.Model):
    __tablename__ = 'anonymous_users'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    session_id = db.Column(db.String(255), unique=True, nullable=False, index=True)
    nickname = db.Column(db.String(80), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_active = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    messages = db.relationship('Message', backref='anonymous_user', lazy='dynamic', foreign_keys='Message.anonymous_user_id')
    room_memberships = db.relationship('RoomMember', backref='anonymous_user', lazy='dynamic')
    
    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'nickname': self.nickname or f'匿名用户{self.id[:8]}',
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_active': self.last_active.isoformat() if self.last_active else None
        }

class InviteCode(db.Model):
    __tablename__ = 'invite_codes'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    created_by = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    used_at = db.Column(db.DateTime, nullable=True)
    used_by = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    note = db.Column(db.Text, nullable=True)
    max_uses = db.Column(db.Integer, default=1)
    current_uses = db.Column(db.Integer, default=0)
    
    # 关系
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_invite_codes')
    user_used = db.relationship('User', foreign_keys=[used_by], backref='used_invite_codes')
    
    def is_valid(self):
        return self.is_active and self.current_uses < self.max_uses
    
    def use_code(self, user_id):
        if not self.is_valid():
            return False
        self.current_uses += 1
        if self.current_uses >= self.max_uses:
            self.used_at = datetime.utcnow()
            self.used_by = user_id
        return True
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'used_at': self.used_at.isoformat() if self.used_at else None,
            'used_by': self.used_by,
            'is_active': self.is_active,
            'note': self.note,
            'max_uses': self.max_uses,
            'current_uses': self.current_uses
        }
