// API 接口管理

class ApiClient {
    constructor() {
        this.baseURL = '';
        this.token = Storage.get('auth_token');
        this.sessionId = Storage.get('session_id');
    }
    
    // 设置认证信息
    setAuth(token, sessionId = null) {
        this.token = token;
        this.sessionId = sessionId;
        
        if (token) {
            Storage.set('auth_token', token);
        } else {
            Storage.remove('auth_token');
        }
        
        if (sessionId) {
            Storage.set('session_id', sessionId);
        } else {
            Storage.remove('session_id');
        }
    }
    
    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        if (this.sessionId) {
            headers['X-Session-ID'] = this.sessionId;
        }
        
        return headers;
    }
    
    // 通用请求方法
    async request(url, options = {}) {
        const config = {
            headers: this.getHeaders(),
            ...options
        };
        
        try {
            const response = await fetch(this.baseURL + url, config);
            
            // 处理认证失败
            if (response.status === 401) {
                this.setAuth(null, null);
                AppState.isAuthenticated = false;
                EventBus.emit('auth:logout');
                throw new Error('认证失败，请重新登录');
            }
            
            // 处理其他HTTP错误
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败');
            }
            throw error;
        }
    }
    
    // GET 请求
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullUrl, {
            method: 'GET'
        });
    }
    
    // POST 请求
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    // PUT 请求
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    // DELETE 请求
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
    
    // 文件上传
    async upload(url, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.entries(additionalData).forEach(([key, value]) => {
            formData.append(key, value);
        });
        
        const headers = {};
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        if (this.sessionId) {
            headers['X-Session-ID'] = this.sessionId;
        }
        
        try {
            const response = await fetch(this.baseURL + url, {
                method: 'POST',
                headers,
                body: formData
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败');
            }
            throw error;
        }
    }
}

// 创建API客户端实例
const api = new ApiClient();

// 认证相关API
const AuthAPI = {
    // 用户登录
    async login(username, password) {
        const response = await api.post('/api/auth/login', {
            username,
            password
        });
        
        if (response.access_token) {
            api.setAuth(response.access_token);
            AppState.isAuthenticated = true;
            AppState.user = response.user;
            AppState.isAdmin = response.user?.is_admin || false;
        }
        
        return response;
    },
    
    // 用户注册
    async register(username, email, password, inviteCode = null) {
        const data = { username, email, password };
        if (inviteCode) {
            data.invite_code = inviteCode;
        }
        
        return api.post('/api/auth/register', data);
    },
    
    // 创建匿名用户
    async createAnonymous(nickname) {
        const response = await api.post('/api/auth/anonymous', {
            nickname
        });
        
        if (response.session_id) {
            api.setAuth(null, response.session_id);
            AppState.isAuthenticated = true;
            AppState.user = response.user;
            AppState.isAdmin = false;
        }
        
        return response;
    },
    
    // 匿名用户转注册用户
    async switchToUser(username, email, password) {
        const response = await api.post('/api/auth/switch-to-user', {
            username,
            email,
            password
        });
        
        if (response.access_token) {
            api.setAuth(response.access_token);
            AppState.user = response.user;
        }
        
        return response;
    },
    
    // 退出登录
    async logout() {
        try {
            await api.post('/api/auth/logout');
        } catch (error) {
            console.warn('退出登录请求失败:', error);
        } finally {
            api.setAuth(null, null);
            AppState.isAuthenticated = false;
            AppState.user = null;
            AppState.isAdmin = false;
            AppState.currentRoom = null;
        }
    }
};

// 房间相关API
const RoomsAPI = {
    // 获取房间列表
    async getRooms() {
        return api.get('/api/rooms/');
    },
    
    // 创建房间
    async createRoom(name, description = '', isPrivate = false, maxMembers = 50) {
        return api.post('/api/rooms/', {
            name,
            description,
            is_private: isPrivate,
            max_members: maxMembers
        });
    },
    
    // 加入房间
    async joinRoom(roomId, password = null) {
        const data = {};
        if (password) {
            data.password = password;
        }
        
        return api.post(`/api/rooms/${roomId}/join`, data);
    },
    
    // 离开房间
    async leaveRoom(roomId) {
        return api.post(`/api/rooms/${roomId}/leave`);
    },
    
    // 获取房间成员
    async getRoomMembers(roomId) {
        return api.get(`/api/rooms/${roomId}/members`);
    },
    
    // 获取房间信息
    async getRoomInfo(roomId) {
        return api.get(`/api/rooms/${roomId}`);
    }
};

// 消息相关API
const MessagesAPI = {
    // 获取消息历史
    async getMessages(roomId, page = 1, limit = 50) {
        return api.get(`/api/messages/${roomId}`, {
            page,
            limit
        });
    },
    
    // 发送消息
    async sendMessage(roomId, content, messageType = 'text', replyTo = null) {
        const data = {
            content,
            message_type: messageType
        };
        
        if (replyTo) {
            data.reply_to = replyTo;
        }
        
        return api.post(`/api/messages/${roomId}`, data);
    },
    
    // 撤回消息
    async recallMessage(messageId) {
        return api.post(`/api/messages/${messageId}/recall`);
    },
    
    // 消息反应
    async reactToMessage(messageId, reaction) {
        return api.post(`/api/messages/${messageId}/react`, {
            reaction
        });
    }
};

// 文件相关API
const FilesAPI = {
    // 上传图片
    async uploadImage(roomId, file) {
        return api.upload(`/api/files/upload/image/${roomId}`, file);
    },
    
    // 上传语音
    async uploadAudio(roomId, file) {
        return api.upload(`/api/files/upload/audio/${roomId}`, file);
    },
    
    // 下载文件
    getDownloadUrl(filePath) {
        return `/api/files/download/${encodeURIComponent(filePath)}`;
    }
};

// 个人信息相关API
const ProfileAPI = {
    // 获取信息分类
    async getCategories() {
        return api.get('/api/profiles/categories');
    },
    
    // 获取我的信息
    async getMyProfile() {
        return api.get('/api/profiles/my-profile');
    },
    
    // 更新我的信息
    async updateMyProfile(profileData) {
        return api.put('/api/profiles/my-profile', profileData);
    },
    
    // 揭露信息
    async revealInfo(roomId, categoryId, infoId) {
        return api.post(`/api/profiles/reveal/${roomId}`, {
            category_id: categoryId,
            info_id: infoId
        });
    },
    
    // 获取用户信息
    async getUserProfile(userId, roomId) {
        return api.get(`/api/profiles/user/${userId}`, {
            room_id: roomId
        });
    }
};

// 问卷相关API
const QuestionnairesAPI = {
    // 获取问卷列表
    async getQuestionnaires() {
        return api.get('/api/questionnaires/');
    },
    
    // 创建问卷
    async createQuestionnaire(title, description, questions) {
        return api.post('/api/questionnaires/', {
            title,
            description,
            questions
        });
    },
    
    // 发送问卷
    async sendQuestionnaire(questionnaireId, roomId) {
        return api.post(`/api/questionnaires/${questionnaireId}/send/${roomId}`);
    },
    
    // 回答问卷
    async respondToQuestionnaire(instanceId, answers) {
        return api.post(`/api/questionnaires/respond/${instanceId}`, {
            answers
        });
    },
    
    // 获取问卷结果
    async getQuestionnaireResults(instanceId) {
        return api.get(`/api/questionnaires/results/${instanceId}`);
    }
};

// 管理员相关API
const AdminAPI = {
    // 获取仪表板数据
    async getDashboard() {
        return api.get('/api/admin/dashboard');
    },
    
    // 获取用户列表
    async getUsers(page = 1, limit = 20) {
        return api.get('/api/admin/users', {
            page,
            limit
        });
    },
    
    // 创建邀请码
    async createInviteCode(maxUses = 1, expiresIn = 7) {
        return api.post('/api/admin/invite-codes', {
            max_uses: maxUses,
            expires_in: expiresIn
        });
    },
    
    // 获取邀请码列表
    async getInviteCodes() {
        return api.get('/api/admin/invite-codes');
    },
    
    // 获取操作日志
    async getLogs(page = 1, limit = 50) {
        return api.get('/api/admin/logs', {
            page,
            limit
        });
    },
    
    // 封禁用户
    async banUser(userId, reason = '') {
        return api.post(`/api/admin/users/${userId}/ban`, {
            reason
        });
    },
    
    // 解封用户
    async unbanUser(userId) {
        return api.post(`/api/admin/users/${userId}/unban`);
    }
};

// 导出API
window.api = api;
window.AuthAPI = AuthAPI;
window.RoomsAPI = RoomsAPI;
window.MessagesAPI = MessagesAPI;
window.FilesAPI = FilesAPI;
window.ProfileAPI = ProfileAPI;
window.QuestionnairesAPI = QuestionnairesAPI;
window.AdminAPI = AdminAPI;
