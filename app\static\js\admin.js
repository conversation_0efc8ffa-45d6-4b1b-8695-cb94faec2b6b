// 管理员界面

class AdminManager {
    constructor() {
        this.stats = {};
        this.users = [];
        this.inviteCodes = [];
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听侧边栏视图切换
        EventBus.on('sidebar:viewChanged', (view) => {
            if (view === 'admin' && AppState.isAdmin) {
                this.loadAdminData();
            }
        });
    }
    
    // 加载管理员数据
    async loadAdminData() {
        try {
            // 加载统计数据
            const statsResponse = await AdminAPI.getDashboard();
            this.stats = statsResponse.stats || {};
            
            // 加载用户列表
            const usersResponse = await AdminAPI.getUsers();
            this.users = usersResponse.users || [];
            
            // 加载邀请码
            const codesResponse = await AdminAPI.getInviteCodes();
            this.inviteCodes = codesResponse.codes || [];
            
            this.renderAdminPanel();
            
        } catch (error) {
            console.error('加载管理员数据失败:', error);
            showNotification('加载管理员数据失败', 'error');
        }
    }
    
    // 渲染管理员面板
    renderAdminPanel() {
        const adminContent = DOM.$('#admin-content');
        if (!adminContent) return;
        
        const content = `
            <div class="admin-dashboard">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-info">
                            <div class="stat-number">${this.stats.total_users || 0}</div>
                            <div class="stat-label">总用户数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏠</div>
                        <div class="stat-info">
                            <div class="stat-number">${this.stats.total_rooms || 0}</div>
                            <div class="stat-label">房间数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💬</div>
                        <div class="stat-info">
                            <div class="stat-number">${this.stats.total_messages || 0}</div>
                            <div class="stat-label">消息数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📋</div>
                        <div class="stat-info">
                            <div class="stat-number">${this.stats.total_questionnaires || 0}</div>
                            <div class="stat-label">问卷数</div>
                        </div>
                    </div>
                </div>
                
                <!-- 管理功能 -->
                <div class="admin-sections">
                    <div class="admin-section">
                        <div class="section-header">
                            <h4>用户管理</h4>
                            <button class="btn btn-primary btn-sm" onclick="window.adminManager.showUserManagement()">管理用户</button>
                        </div>
                        <div class="section-content">
                            <p>在线用户: ${this.stats.online_users || 0}</p>
                            <p>今日新增: ${this.stats.new_users_today || 0}</p>
                        </div>
                    </div>
                    
                    <div class="admin-section">
                        <div class="section-header">
                            <h4>邀请码管理</h4>
                            <button class="btn btn-primary btn-sm" onclick="window.adminManager.showInviteCodeManagement()">管理邀请码</button>
                        </div>
                        <div class="section-content">
                            <p>有效邀请码: ${this.inviteCodes.filter(c => c.is_active).length}</p>
                            <p>已使用: ${this.inviteCodes.filter(c => c.used_count > 0).length}</p>
                        </div>
                    </div>
                    
                    <div class="admin-section">
                        <div class="section-header">
                            <h4>系统设置</h4>
                            <button class="btn btn-primary btn-sm" onclick="window.adminManager.showSystemSettings()">系统设置</button>
                        </div>
                        <div class="section-content">
                            <p>服务器状态: 正常</p>
                            <p>数据库大小: ${this.stats.db_size || '未知'}</p>
                        </div>
                    </div>
                    
                    <div class="admin-section">
                        <div class="section-header">
                            <h4>日志管理</h4>
                            <button class="btn btn-primary btn-sm" onclick="window.adminManager.showLogs()">查看日志</button>
                        </div>
                        <div class="section-content">
                            <p>今日错误: ${this.stats.errors_today || 0}</p>
                            <p>系统警告: ${this.stats.warnings_today || 0}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        adminContent.innerHTML = content;
    }
    
    // 显示用户管理
    showUserManagement() {
        const usersHTML = this.users.map(user => `
            <tr>
                <td>${StringUtils.escapeHtml(user.username)}</td>
                <td>${StringUtils.escapeHtml(user.email || '未设置')}</td>
                <td>${user.is_anonymous ? '匿名' : '注册'}</td>
                <td>${user.is_online ? '在线' : '离线'}</td>
                <td>${TimeUtils.formatDateTime(user.created_at)}</td>
                <td>
                    <button class="btn btn-warning btn-sm" onclick="window.adminManager.toggleUserStatus('${user.id}', ${!user.is_active})">
                        ${user.is_active ? '禁用' : '启用'}
                    </button>
                    ${!user.is_anonymous ? `
                        <button class="btn btn-danger btn-sm" onclick="window.adminManager.deleteUser('${user.id}')">删除</button>
                    ` : ''}
                </td>
            </tr>
        `).join('');
        
        const content = `
            <div class="user-management">
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${usersHTML}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        window.app.showModal(content, '用户管理');
    }
    
    // 显示邀请码管理
    showInviteCodeManagement() {
        const codesHTML = this.inviteCodes.map(code => `
            <tr>
                <td><code>${code.code}</code></td>
                <td>${code.max_uses || '无限制'}</td>
                <td>${code.used_count || 0}</td>
                <td>${code.is_active ? '有效' : '无效'}</td>
                <td>${code.expires_at ? TimeUtils.formatDateTime(code.expires_at) : '永不过期'}</td>
                <td>
                    <button class="btn btn-warning btn-sm" onclick="window.adminManager.toggleCodeStatus('${code.id}', ${!code.is_active})">
                        ${code.is_active ? '禁用' : '启用'}
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="window.adminManager.deleteCode('${code.id}')">删除</button>
                </td>
            </tr>
        `).join('');
        
        const content = `
            <div class="invite-code-management">
                <div class="section-header">
                    <button class="btn btn-primary" onclick="window.adminManager.showCreateCodeDialog()">创建邀请码</button>
                </div>
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>邀请码</th>
                                <th>最大使用次数</th>
                                <th>已使用次数</th>
                                <th>状态</th>
                                <th>过期时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${codesHTML}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        window.app.showModal(content, '邀请码管理');
    }
    
    // 显示创建邀请码对话框
    showCreateCodeDialog() {
        const content = `
            <div class="form-group">
                <label class="form-label">邀请码（留空自动生成）</label>
                <input type="text" id="invite-code" class="form-control" placeholder="自动生成">
            </div>
            <div class="form-group">
                <label class="form-label">最大使用次数</label>
                <input type="number" id="max-uses" class="form-control" value="1" min="1">
            </div>
            <div class="form-group">
                <label class="form-label">过期时间</label>
                <input type="datetime-local" id="expires-at" class="form-control">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="window.adminManager.createInviteCode()">创建</button>
            </div>
        `;
        
        window.app.showModal(content, '创建邀请码');
    }
    
    // 创建邀请码
    async createInviteCode() {
        const code = DOM.$('#invite-code').value.trim();
        const maxUses = parseInt(DOM.$('#max-uses').value) || 1;
        const expiresAt = DOM.$('#expires-at').value;
        
        try {
            await AdminAPI.createInviteCode(code || null, maxUses, expiresAt || null);
            
            window.app.closeModal();
            showNotification('邀请码创建成功', 'success');
            
            // 重新加载数据
            await this.loadAdminData();
            
        } catch (error) {
            console.error('创建邀请码失败:', error);
            showNotification('创建邀请码失败', 'error');
        }
    }
    
    // 切换用户状态
    async toggleUserStatus(userId, isActive) {
        try {
            await AdminAPI.updateUser(userId, { is_active: isActive });
            showNotification(`用户已${isActive ? '启用' : '禁用'}`, 'success');
            
            // 重新加载数据
            await this.loadAdminData();
            
        } catch (error) {
            console.error('更新用户状态失败:', error);
            showNotification('操作失败', 'error');
        }
    }
    
    // 删除用户
    async deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？此操作不可恢复。')) {
            return;
        }
        
        try {
            await AdminAPI.deleteUser(userId);
            showNotification('用户已删除', 'success');
            
            // 重新加载数据
            await this.loadAdminData();
            
        } catch (error) {
            console.error('删除用户失败:', error);
            showNotification('删除用户失败', 'error');
        }
    }
    
    // 切换邀请码状态
    async toggleCodeStatus(codeId, isActive) {
        try {
            await AdminAPI.updateInviteCode(codeId, { is_active: isActive });
            showNotification(`邀请码已${isActive ? '启用' : '禁用'}`, 'success');
            
            // 重新加载数据
            await this.loadAdminData();
            
        } catch (error) {
            console.error('更新邀请码状态失败:', error);
            showNotification('操作失败', 'error');
        }
    }
    
    // 删除邀请码
    async deleteCode(codeId) {
        if (!confirm('确定要删除这个邀请码吗？')) {
            return;
        }
        
        try {
            await AdminAPI.deleteInviteCode(codeId);
            showNotification('邀请码已删除', 'success');
            
            // 重新加载数据
            await this.loadAdminData();
            
        } catch (error) {
            console.error('删除邀请码失败:', error);
            showNotification('删除邀请码失败', 'error');
        }
    }
    
    // 显示系统设置
    showSystemSettings() {
        const content = `
            <div class="system-settings">
                <div class="form-group">
                    <label class="form-label">系统名称</label>
                    <input type="text" id="system-name" class="form-control" value="聊天平台">
                </div>
                <div class="form-group">
                    <label class="form-label">最大房间数</label>
                    <input type="number" id="max-rooms" class="form-control" value="100">
                </div>
                <div class="form-group">
                    <label class="form-label">最大用户数</label>
                    <input type="number" id="max-users" class="form-control" value="1000">
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="allow-anonymous" checked>
                        <label for="allow-anonymous">允许匿名用户</label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="require-invite" checked>
                        <label for="require-invite">注册需要邀请码</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="window.adminManager.saveSystemSettings()">保存</button>
                </div>
            </div>
        `;
        
        window.app.showModal(content, '系统设置');
    }
    
    // 保存系统设置
    saveSystemSettings() {
        showNotification('系统设置功能开发中', 'info');
        window.app.closeModal();
    }
    
    // 显示日志
    showLogs() {
        const content = `
            <div class="logs-viewer">
                <div class="logs-filters">
                    <select class="form-select" id="log-level">
                        <option value="">所有级别</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                    </select>
                    <input type="date" id="log-date" class="form-control">
                    <button class="btn btn-primary" onclick="window.adminManager.loadLogs()">刷新</button>
                </div>
                <div class="logs-content" id="logs-content">
                    <div class="empty-state">
                        <div class="empty-state-icon">📄</div>
                        <div class="empty-state-title">日志功能开发中</div>
                        <div class="empty-state-description">即将支持查看系统日志</div>
                    </div>
                </div>
            </div>
        `;
        
        window.app.showModal(content, '系统日志');
    }
    
    // 加载日志
    loadLogs() {
        showNotification('日志功能开发中', 'info');
    }
}

// 初始化管理员管理器
document.addEventListener('DOMContentLoaded', () => {
    if (AppState.isAdmin) {
        window.adminManager = new AdminManager();
    }
});
