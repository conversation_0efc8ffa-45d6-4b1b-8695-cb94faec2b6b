from . import db, generate_uuid
from datetime import datetime
import json

class Questionnaire(db.Model):
    __tablename__ = 'questionnaires'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_by = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=True)
    anonymous_created_by = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('anonymous_users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)
    is_template = db.Column(db.Bo<PERSON>an, default=False)  # 是否为模板问卷
    
    # 关系
    questions = db.relationship('Question', backref='questionnaire', lazy='dynamic', cascade='all, delete-orphan', order_by='Question.sort_order')
    responses = db.relationship('QuestionnaireResponse', backref='questionnaire', lazy='dynamic')
    
    # 确保创建者为用户或匿名用户之一
    __table_args__ = (
        db.CheckConstraint('(created_by IS NOT NULL) != (anonymous_created_by IS NOT NULL)', 
                          name='check_questionnaire_creator'),
    )
    
    def get_creator_id(self):
        return self.created_by or self.anonymous_created_by
    
    def get_creator_name(self):
        if self.created_by:
            from .user import User
            user = User.query.get(self.created_by)
            return user.username if user else '未知用户'
        elif self.anonymous_created_by:
            from .user import AnonymousUser
            user = AnonymousUser.query.get(self.anonymous_created_by)
            return user.nickname or f'匿名用户{user.id[:8]}' if user else '未知用户'
        return '系统'
    
    def to_dict(self, include_questions=True):
        data = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'created_by': self.get_creator_id(),
            'creator_name': self.get_creator_name(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_active': self.is_active,
            'is_template': self.is_template,
            'question_count': self.questions.count()
        }
        
        if include_questions:
            data['questions'] = [q.to_dict() for q in self.questions.filter_by(is_active=True)]
        
        return data

class Question(db.Model):
    __tablename__ = 'questions'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    questionnaire_id = db.Column(db.String(36), db.ForeignKey('questionnaires.id'), nullable=False)
    question_text = db.Column(db.Text, nullable=False)
    question_type = db.Column(db.String(20), default='single_choice')  # single_choice, multiple_choice, text, rating, yes_no
    is_required = db.Column(db.Boolean, default=True)
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    extra_data = db.Column(db.Text, nullable=True)  # JSON格式存储额外配置
    
    # 关系
    options = db.relationship('QuestionOption', backref='question', lazy='dynamic', cascade='all, delete-orphan', order_by='QuestionOption.sort_order')
    responses = db.relationship('QuestionResponse', backref='question', lazy='dynamic')
    
    def set_metadata(self, data):
        self.extra_data = json.dumps(data) if data else None

    def get_metadata(self):
        return json.loads(self.extra_data) if self.extra_data else {}
    
    def to_dict(self, include_options=True):
        data = {
            'id': self.id,
            'questionnaire_id': self.questionnaire_id,
            'question_text': self.question_text,
            'question_type': self.question_type,
            'is_required': self.is_required,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'metadata': self.get_metadata()
        }
        
        if include_options and self.question_type in ['single_choice', 'multiple_choice']:
            data['options'] = [opt.to_dict() for opt in self.options.filter_by(is_active=True)]
        
        return data

class QuestionOption(db.Model):
    __tablename__ = 'question_options'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    question_id = db.Column(db.String(36), db.ForeignKey('questions.id'), nullable=False)
    option_text = db.Column(db.String(500), nullable=False)
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'question_id': self.question_id,
            'option_text': self.option_text,
            'sort_order': self.sort_order,
            'is_active': self.is_active
        }

class QuestionnaireResponse(db.Model):
    __tablename__ = 'questionnaire_responses'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    questionnaire_id = db.Column(db.String(36), db.ForeignKey('questionnaires.id'), nullable=False)
    instance_id = db.Column(db.String(36), nullable=False, index=True)  # 问卷实例ID，用于区分同一问卷的多次发送
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)
    anonymous_user_id = db.Column(db.String(36), db.ForeignKey('anonymous_users.id'), nullable=True)
    room_id = db.Column(db.String(36), db.ForeignKey('rooms.id'), nullable=False)
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)
    is_completed = db.Column(db.Boolean, default=False)
    
    # 关系
    question_responses = db.relationship('QuestionResponse', backref='questionnaire_response', lazy='dynamic', cascade='all, delete-orphan')
    
    # 确保回答者为用户或匿名用户之一
    __table_args__ = (
        db.CheckConstraint('(user_id IS NOT NULL) != (anonymous_user_id IS NOT NULL)', 
                          name='check_response_user_or_anonymous'),
        db.UniqueConstraint('instance_id', 'user_id', name='unique_instance_user'),
        db.UniqueConstraint('instance_id', 'anonymous_user_id', name='unique_instance_anonymous')
    )
    
    def get_user_id(self):
        return self.user_id or self.anonymous_user_id
    
    def get_user_name(self):
        if self.user_id:
            from .user import User
            user = User.query.get(self.user_id)
            return user.username if user else '未知用户'
        elif self.anonymous_user_id:
            from .user import AnonymousUser
            user = AnonymousUser.query.get(self.anonymous_user_id)
            return user.nickname or f'匿名用户{user.id[:8]}' if user else '未知用户'
        return '未知用户'
    
    def complete(self):
        self.is_completed = True
        self.completed_at = datetime.utcnow()
    
    def to_dict(self, include_responses=False):
        data = {
            'id': self.id,
            'questionnaire_id': self.questionnaire_id,
            'instance_id': self.instance_id,
            'user_id': self.get_user_id(),
            'user_name': self.get_user_name(),
            'room_id': self.room_id,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'is_completed': self.is_completed
        }
        
        if include_responses:
            data['responses'] = [resp.to_dict() for resp in self.question_responses]
        
        return data

class QuestionResponse(db.Model):
    __tablename__ = 'question_responses'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    questionnaire_response_id = db.Column(db.String(36), db.ForeignKey('questionnaire_responses.id'), nullable=False)
    question_id = db.Column(db.String(36), db.ForeignKey('questions.id'), nullable=False)
    answer = db.Column(db.Text, nullable=False)  # JSON格式存储答案
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 确保同一问卷回答中每个问题只能回答一次
    __table_args__ = (
        db.UniqueConstraint('questionnaire_response_id', 'question_id', name='unique_question_response'),
    )
    
    def set_answer(self, answer):
        if isinstance(answer, (list, dict)):
            self.answer = json.dumps(answer)
        else:
            self.answer = str(answer)
    
    def get_answer(self):
        try:
            return json.loads(self.answer)
        except:
            return self.answer
    
    def get_display_answer(self):
        answer = self.get_answer()
        if isinstance(answer, list):
            return ', '.join(answer)
        return str(answer)
    
    def to_dict(self):
        return {
            'id': self.id,
            'questionnaire_response_id': self.questionnaire_response_id,
            'question_id': self.question_id,
            'question_text': self.question.question_text,
            'answer': self.get_answer(),
            'display_answer': self.get_display_answer(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
