from flask import Blueprint, request
from app.models import db
from app.models.message import Message, MessageReaction
from app.models.room import RoomMember
from app.utils.helpers import (success_response, error_response, require_auth, 
                              validate_request_data, paginate_query)
from datetime import datetime

bp = Blueprint('messages', __name__)

@bp.route('/<room_id>', methods=['GET'])
@require_auth()
def get_messages(room_id):
    """获取房间消息历史"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    # 按时间倒序查询消息
    query = Message.query.filter_by(room_id=room_id).order_by(Message.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    # 反转消息顺序，使最新消息在最后
    messages = list(reversed(result['items']))
    
    return success_response({
        'messages': [msg.to_dict() for msg in messages],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/<room_id>', methods=['POST'])
@require_auth()
def send_message(room_id):
    """发送消息"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['content'])
    if not valid:
        return error_response(error_msg)
    
    content = data['content'].strip()
    message_type = data.get('message_type', 'text')
    reply_to_id = data.get('reply_to_id')
    
    if not content:
        return error_response("消息内容不能为空")
    
    if len(content) > 2000:
        return error_response("消息内容过长")
    
    # 验证回复消息
    reply_to = None
    if reply_to_id:
        reply_to = Message.query.filter_by(id=reply_to_id, room_id=room_id).first()
        if not reply_to:
            return error_response("回复的消息不存在")
    
    try:
        message = Message(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            content=content,
            message_type=message_type,
            reply_to_id=reply_to_id
        )
        
        db.session.add(message)
        db.session.commit()
        
        # 通过WebSocket广播消息
        from app import socketio
        socketio.emit('new_message', {
            'message': message.to_dict()
        }, room=room_id)
        
        return success_response({
            'message': message.to_dict()
        }, "消息发送成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"发送消息失败: {str(e)}")

@bp.route('/<message_id>/recall', methods=['POST'])
@require_auth()
def recall_message(message_id):
    """撤回消息"""
    user = request.current_user
    user_type = request.user_type
    
    message = Message.query.get_or_404(message_id)
    
    # 检查权限
    is_admin = user_type == 'user' and user.is_admin
    can_recall = message.can_recall(user.id, is_admin)
    
    if not can_recall:
        return error_response("无法撤回此消息")
    
    try:
        message.recall(user.id)
        db.session.commit()
        
        # 通过WebSocket广播撤回事件
        from app import socketio
        socketio.emit('message_recalled', {
            'message_id': message_id,
            'recalled_by': user.id
        }, room=message.room_id)
        
        return success_response(message="消息已撤回")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"撤回消息失败: {str(e)}")

@bp.route('/<message_id>/react', methods=['POST'])
@require_auth()
def react_to_message(message_id):
    """对消息添加反应"""
    user = request.current_user
    user_type = request.user_type
    
    message = Message.query.get_or_404(message_id)
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=message.room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=message.room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['reaction'])
    if not valid:
        return error_response(error_msg)
    
    reaction = data['reaction'].strip()
    
    if not reaction:
        return error_response("反应不能为空")
    
    try:
        # 检查是否已经有相同反应
        existing = None
        if user_type == 'user':
            existing = MessageReaction.query.filter_by(
                message_id=message_id, user_id=user.id, reaction=reaction
            ).first()
        else:
            existing = MessageReaction.query.filter_by(
                message_id=message_id, anonymous_user_id=user.id, reaction=reaction
            ).first()
        
        if existing:
            # 移除反应
            db.session.delete(existing)
            action = 'removed'
        else:
            # 添加反应
            reaction_obj = MessageReaction(
                message_id=message_id,
                user_id=user.id if user_type == 'user' else None,
                anonymous_user_id=user.id if user_type == 'anonymous' else None,
                reaction=reaction
            )
            db.session.add(reaction_obj)
            action = 'added'
        
        db.session.commit()
        
        # 通过WebSocket广播反应事件
        from app import socketio
        socketio.emit('message_reaction', {
            'message_id': message_id,
            'user_id': user.id,
            'reaction': reaction,
            'action': action
        }, room=message.room_id)
        
        return success_response({
            'action': action,
            'reaction': reaction
        }, f"反应已{action}")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"操作失败: {str(e)}")

@bp.route('/<message_id>/reactions', methods=['GET'])
@require_auth()
def get_message_reactions(message_id):
    """获取消息的所有反应"""
    message = Message.query.get_or_404(message_id)
    
    reactions = MessageReaction.query.filter_by(message_id=message_id).all()
    
    # 按反应类型分组统计
    reaction_counts = {}
    for reaction in reactions:
        emoji = reaction.reaction
        if emoji not in reaction_counts:
            reaction_counts[emoji] = {
                'count': 0,
                'users': []
            }
        reaction_counts[emoji]['count'] += 1
        reaction_counts[emoji]['users'].append({
            'user_id': reaction.get_user_id(),
            'created_at': reaction.created_at.isoformat()
        })
    
    return success_response({
        'reactions': reaction_counts,
        'total_reactions': len(reactions)
    })
