from flask import Blueprint, request, url_for
from app.models import db
from app.models.room import Room, RoomMember, RoomInviteLink
from app.models.user import User
from app.utils.helpers import (success_response, error_response, require_auth, 
                              validate_request_data, paginate_query, get_current_user)
from datetime import datetime, timedelta
import uuid

bp = Blueprint('rooms', __name__)

@bp.route('/', methods=['GET'])
@require_auth()
def list_rooms():
    """获取房间列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    is_public = request.args.get('public', type=bool)
    
    query = Room.query.filter_by(is_active=True)
    
    if is_public is not None:
        query = query.filter_by(is_public=is_public)
    
    query = query.order_by(Room.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'rooms': [room.to_dict() for room in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/', methods=['POST'])
@require_auth(allow_anonymous=False)
def create_room():
    """创建房间"""
    user = request.current_user
    
    # 检查用户是否有创建房间的权限
    if not user.can_create_room and not user.is_admin:
        return error_response("您没有创建房间的权限，请联系管理员开通")
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['name'])
    if not valid:
        return error_response(error_msg)
    
    name = data['name'].strip()
    description = data.get('description', '').strip()
    is_public = data.get('is_public', False)
    password = data.get('password', '').strip()
    max_members = data.get('max_members', 100)
    
    if len(name) < 2 or len(name) > 50:
        return error_response("房间名称长度应在2-50字符之间")
    
    if max_members < 2 or max_members > 1000:
        return error_response("房间最大成员数应在2-1000之间")
    
    try:
        room = Room(
            name=name,
            description=description,
            creator_id=user.id,
            is_public=is_public,
            max_members=max_members
        )
        
        if password:
            room.set_password(password)
        
        db.session.add(room)
        db.session.flush()  # 获取房间ID
        
        # 创建者自动加入房间
        member = RoomMember(
            room_id=room.id,
            user_id=user.id,
            role='admin'
        )
        db.session.add(member)
        
        db.session.commit()
        
        return success_response({
            'room': room.to_dict()
        }, "房间创建成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建房间失败: {str(e)}")

@bp.route('/<room_id>', methods=['GET'])
@require_auth()
def get_room(room_id):
    """获取房间详情"""
    room = Room.query.get_or_404(room_id)
    
    if not room.is_active:
        return error_response("房间不存在或已被禁用", 404)
    
    return success_response({
        'room': room.to_dict()
    })

@bp.route('/<room_id>/join', methods=['POST'])
@require_auth()
def join_room(room_id):
    """加入房间"""
    room = Room.query.get_or_404(room_id)
    user = request.current_user
    user_type = request.user_type
    
    if not room.is_active:
        return error_response("房间不存在或已被禁用", 404)
    
    if not room.can_join():
        return error_response("房间已满或不可加入")
    
    # 检查是否已经是成员
    existing_member = None
    if user_type == 'user':
        existing_member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        existing_member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if existing_member:
        return error_response("您已经是房间成员")
    
    data = request.get_json() or {}
    password = data.get('password', '')
    
    # 检查密码
    if room.has_password() and not room.check_password(password):
        return error_response("房间密码错误")
    
    try:
        member = RoomMember(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None
        )
        db.session.add(member)
        db.session.commit()
        
        return success_response({
            'member': member.to_dict()
        }, "成功加入房间")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"加入房间失败: {str(e)}")

@bp.route('/<room_id>/leave', methods=['POST'])
@require_auth()
def leave_room(room_id):
    """离开房间"""
    user = request.current_user
    user_type = request.user_type
    
    # 查找成员记录
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员")
    
    try:
        member.is_active = False
        member.left_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(message="成功离开房间")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"离开房间失败: {str(e)}")

@bp.route('/<room_id>/members', methods=['GET'])
@require_auth()
def get_room_members(room_id):
    """获取房间成员列表"""
    room = Room.query.get_or_404(room_id)
    
    if not room.is_active:
        return error_response("房间不存在或已被禁用", 404)
    
    members = RoomMember.query.filter_by(room_id=room_id, is_active=True).all()
    
    return success_response({
        'members': [member.to_dict() for member in members],
        'total': len(members),
        'online': sum(1 for m in members if m.is_online)
    })

@bp.route('/<room_id>/invite-link', methods=['POST'])
@require_auth(allow_anonymous=False)
def create_invite_link(room_id):
    """创建邀请链接"""
    room = Room.query.get_or_404(room_id)
    user = request.current_user
    
    # 检查权限（房间创建者或管理员）
    if room.creator_id != user.id and not user.is_admin:
        return error_response("没有权限创建邀请链接")
    
    data = request.get_json() or {}
    max_uses = data.get('max_uses', 10)
    expires_hours = data.get('expires_hours', 24)
    
    if max_uses < 1 or max_uses > 1000:
        return error_response("最大使用次数应在1-1000之间")
    
    if expires_hours < 1 or expires_hours > 168:  # 最长7天
        return error_response("过期时间应在1-168小时之间")
    
    try:
        invite_link = RoomInviteLink(
            room_id=room_id,
            created_by=user.id,
            max_uses=max_uses,
            expires_at=datetime.utcnow() + timedelta(hours=expires_hours)
        )
        db.session.add(invite_link)
        db.session.commit()
        
        base_url = request.url_root.rstrip('/')
        
        return success_response({
            'invite_link': invite_link.to_dict(base_url)
        }, "邀请链接创建成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建邀请链接失败: {str(e)}")

@bp.route('/join-by-token/<token>', methods=['POST'])
@require_auth()
def join_by_token(token):
    """通过邀请链接加入房间"""
    invite_link = RoomInviteLink.query.filter_by(token=token).first()
    
    if not invite_link or not invite_link.is_valid():
        return error_response("邀请链接无效或已过期")
    
    room = invite_link.room
    if not room.is_active or not room.can_join():
        return error_response("房间不可用或已满")
    
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否已经是成员
    existing_member = None
    if user_type == 'user':
        existing_member = RoomMember.query.filter_by(
            room_id=room.id, user_id=user.id, is_active=True
        ).first()
    else:
        existing_member = RoomMember.query.filter_by(
            room_id=room.id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if existing_member:
        return success_response({
            'room': room.to_dict(),
            'member': existing_member.to_dict()
        }, "您已经是房间成员")
    
    try:
        # 使用邀请链接
        if not invite_link.use_link():
            return error_response("邀请链接使用次数已达上限")
        
        # 加入房间
        member = RoomMember(
            room_id=room.id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None
        )
        db.session.add(member)
        db.session.commit()
        
        return success_response({
            'room': room.to_dict(),
            'member': member.to_dict()
        }, "成功加入房间")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"加入房间失败: {str(e)}")
