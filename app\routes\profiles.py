from flask import Blueprint, request
from app.models import db
from app.models.profile import ProfileCategory, ProfileField, UserProfile, ProfileReveal
from app.models.room import RoomMember
from app.models.message import Message
from app.utils.helpers import (success_response, error_response, require_auth, require_admin,
                              validate_request_data, log_admin_action)

bp = Blueprint('profiles', __name__)

@bp.route('/categories', methods=['GET'])
@require_auth()
def get_categories():
    """获取所有人物信息分类"""
    categories = ProfileCategory.query.filter_by(is_active=True).order_by(ProfileCategory.sort_order).all()
    
    return success_response({
        'categories': [cat.to_dict() for cat in categories]
    })

@bp.route('/categories', methods=['POST'])
@require_admin()
def create_category():
    """创建人物信息分类"""
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['name', 'display_name'])
    if not valid:
        return error_response(error_msg)
    
    name = data['name'].strip()
    display_name = data['display_name'].strip()
    description = data.get('description', '').strip()
    sort_order = data.get('sort_order', 0)
    
    if ProfileCategory.query.filter_by(name=name).first():
        return error_response("分类名称已存在")
    
    try:
        category = ProfileCategory(
            name=name,
            display_name=display_name,
            description=description,
            sort_order=sort_order
        )
        db.session.add(category)
        db.session.commit()
        
        log_admin_action('create_profile_category', 'profile_category', category.id, {
            'name': name,
            'display_name': display_name
        })
        
        return success_response({
            'category': category.to_dict()
        }, "分类创建成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建分类失败: {str(e)}")

@bp.route('/categories/<category_id>/fields', methods=['POST'])
@require_admin()
def create_field(category_id):
    """创建人物信息字段"""
    category = ProfileCategory.query.get_or_404(category_id)
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['name', 'display_name', 'field_type'])
    if not valid:
        return error_response(error_msg)
    
    name = data['name'].strip()
    display_name = data['display_name'].strip()
    field_type = data['field_type']
    options = data.get('options', [])
    is_required = data.get('is_required', False)
    max_selections = data.get('max_selections')
    sort_order = data.get('sort_order', 0)
    
    if field_type not in ['single_choice', 'multiple_choice', 'text', 'number']:
        return error_response("无效的字段类型")
    
    if ProfileField.query.filter_by(category_id=category_id, name=name).first():
        return error_response("字段名称在该分类下已存在")
    
    try:
        field = ProfileField(
            category_id=category_id,
            name=name,
            display_name=display_name,
            field_type=field_type,
            is_required=is_required,
            max_selections=max_selections,
            sort_order=sort_order
        )
        
        if options:
            field.set_options(options)
        
        db.session.add(field)
        db.session.commit()
        
        log_admin_action('create_profile_field', 'profile_field', field.id, {
            'category_id': category_id,
            'name': name,
            'display_name': display_name,
            'field_type': field_type
        })
        
        return success_response({
            'field': field.to_dict()
        }, "字段创建成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建字段失败: {str(e)}")

@bp.route('/my-profile', methods=['GET'])
@require_auth()
def get_my_profile():
    """获取当前用户的人物信息"""
    user = request.current_user
    user_type = request.user_type
    
    profiles = None
    if user_type == 'user':
        profiles = UserProfile.query.filter_by(user_id=user.id).all()
    else:
        profiles = UserProfile.query.filter_by(anonymous_user_id=user.id).all()
    
    return success_response({
        'profiles': [profile.to_dict(show_hidden=True) for profile in profiles]
    })

@bp.route('/my-profile', methods=['POST'])
@require_auth()
def update_my_profile():
    """更新当前用户的人物信息"""
    user = request.current_user
    user_type = request.user_type
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['field_id', 'value'])
    if not valid:
        return error_response(error_msg)
    
    field_id = data['field_id']
    value = data['value']
    is_hidden = data.get('is_hidden', False)
    
    field = ProfileField.query.get_or_404(field_id)
    
    # 验证值
    if field.field_type in ['single_choice', 'multiple_choice']:
        options = field.get_options()
        if field.field_type == 'single_choice':
            if value not in options:
                return error_response("选择的值不在可选项中")
        else:  # multiple_choice
            if not isinstance(value, list):
                return error_response("多选字段的值必须是数组")
            if not all(v in options for v in value):
                return error_response("选择的值不在可选项中")
            if field.max_selections and len(value) > field.max_selections:
                return error_response(f"最多只能选择{field.max_selections}个选项")
    
    try:
        # 查找或创建用户配置
        profile = None
        if user_type == 'user':
            profile = UserProfile.query.filter_by(user_id=user.id, field_id=field_id).first()
        else:
            profile = UserProfile.query.filter_by(anonymous_user_id=user.id, field_id=field_id).first()
        
        if profile:
            profile.set_value(value)
            profile.is_hidden = is_hidden
        else:
            profile = UserProfile(
                user_id=user.id if user_type == 'user' else None,
                anonymous_user_id=user.id if user_type == 'anonymous' else None,
                field_id=field_id,
                is_hidden=is_hidden
            )
            profile.set_value(value)
            db.session.add(profile)
        
        db.session.commit()
        
        return success_response({
            'profile': profile.to_dict(show_hidden=True)
        }, "人物信息更新成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"更新失败: {str(e)}")

@bp.route('/user/<user_id>/profile', methods=['GET'])
@require_auth()
def get_user_profile(user_id):
    """获取指定用户的人物信息（仅显示非隐藏的）"""
    current_user = request.current_user
    current_user_type = request.user_type
    
    # 管理员可以查看所有信息
    show_hidden = current_user_type == 'user' and current_user.is_admin
    
    # 查询用户配置
    profiles = UserProfile.query.filter(
        (UserProfile.user_id == user_id) | (UserProfile.anonymous_user_id == user_id)
    ).all()
    
    # 统计隐藏信息数量
    hidden_count = sum(1 for p in profiles if p.is_hidden)
    
    return success_response({
        'profiles': [profile.to_dict(show_hidden=show_hidden) for profile in profiles],
        'hidden_count': hidden_count if not show_hidden else 0
    })

@bp.route('/reveal/<room_id>', methods=['POST'])
@require_auth()
def reveal_profile(room_id):
    """在房间中揭露人物信息"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['profile_id'])
    if not valid:
        return error_response(error_msg)
    
    profile_id = data['profile_id']
    
    # 验证配置是否属于当前用户
    profile = None
    if user_type == 'user':
        profile = UserProfile.query.filter_by(id=profile_id, user_id=user.id).first()
    else:
        profile = UserProfile.query.filter_by(id=profile_id, anonymous_user_id=user.id).first()
    
    if not profile:
        return error_response("人物信息不存在或不属于您")
    
    if not profile.is_hidden:
        return error_response("该信息已经是公开的")
    
    # 检查是否已经在该房间揭露过
    existing_reveal = ProfileReveal.query.filter_by(
        profile_id=profile_id, room_id=room_id
    ).first()
    
    if existing_reveal:
        return error_response("该信息已经在此房间揭露过")
    
    try:
        # 创建揭露记录
        reveal = ProfileReveal(
            profile_id=profile_id,
            room_id=room_id
        )
        db.session.add(reveal)
        db.session.flush()  # 获取reveal.id
        
        # 创建系统消息
        message = Message(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            content=f"揭露了自己的{profile.field.category.display_name} - {profile.field.display_name}: {profile.get_display_value()}",
            message_type='profile_reveal'
        )
        message.set_metadata({
            'profile_id': profile_id,
            'reveal_id': reveal.id,
            'category_name': profile.field.category.display_name,
            'field_name': profile.field.display_name,
            'value': profile.get_display_value()
        })
        
        db.session.add(message)
        db.session.flush()
        
        # 关联消息
        reveal.message_id = message.id
        
        db.session.commit()
        
        # 通过WebSocket广播揭露事件
        from app import socketio
        socketio.emit('profile_revealed', {
            'message': message.to_dict(),
            'reveal': reveal.to_dict()
        }, room=room_id)
        
        return success_response({
            'reveal': reveal.to_dict(),
            'message': message.to_dict()
        }, "人物信息已揭露")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"揭露失败: {str(e)}")

@bp.route('/reveals/<room_id>', methods=['GET'])
@require_auth()
def get_room_reveals(room_id):
    """获取房间中的所有揭露记录"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    reveals = ProfileReveal.query.filter_by(room_id=room_id).order_by(ProfileReveal.revealed_at.desc()).all()
    
    return success_response({
        'reveals': [reveal.to_dict() for reveal in reveals]
    })
