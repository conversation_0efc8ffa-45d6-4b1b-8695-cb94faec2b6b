#!/usr/bin/env python3
"""
聊天平台启动文件
"""

from app import create_app, socketio

if __name__ == '__main__':
    app = create_app()

    print("🚀 聊天平台启动中...")
    print("📍 访问地址: http://localhost:5000")
    print("📊 API测试页面: http://localhost:5000")
    print("📋 API信息: http://localhost:5000/api")
    print("⚙️  默认管理员: admin / admin123")
    print("=" * 50)

    # 启动SocketIO服务器
    socketio.run(
        app,
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False,  # 避免重复初始化
        log_output=True,
        allow_unsafe_werkzeug=True  # 允许在开发环境使用
    )
