from flask import Blueprint, request, current_app, send_file
from werkzeug.utils import secure_filename
from app.models import db
from app.models.message import Message
from app.models.room import RoomMember
from app.utils.helpers import (success_response, error_response, require_auth, 
                              allowed_file, secure_filename as custom_secure_filename,
                              compress_image, get_file_size)
import os
import uuid

bp = Blueprint('files', __name__)

@bp.route('/upload/image/<room_id>', methods=['POST'])
@require_auth()
def upload_image(room_id):
    """上传图片"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    if 'file' not in request.files:
        return error_response("没有选择文件")
    
    file = request.files['file']
    if file.filename == '':
        return error_response("没有选择文件")
    
    if not allowed_file(file.filename, current_app.config['ALLOWED_IMAGE_EXTENSIONS']):
        return error_response("不支持的图片格式")
    
    try:
        # 生成安全的文件名
        filename = custom_secure_filename(file.filename)
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'images', filename)
        
        # 保存文件
        file.save(file_path)
        
        # 检查文件大小
        file_size = get_file_size(file_path)
        if file_size > current_app.config['MAX_CONTENT_LENGTH']:
            os.remove(file_path)
            return error_response("文件大小超过限制")
        
        # 压缩图片
        if not compress_image(file_path, current_app.config['MAX_IMAGE_SIZE'], 
                             current_app.config['IMAGE_COMPRESSION_QUALITY']):
            os.remove(file_path)
            return error_response("图片处理失败")
        
        # 重新获取压缩后的文件大小
        file_size = get_file_size(file_path)
        
        # 创建消息记录
        message = Message(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            content=f"[图片] {file.filename}",
            message_type='image',
            file_path=f"images/{filename}",
            file_name=file.filename,
            file_size=file_size
        )
        
        db.session.add(message)
        db.session.commit()
        
        # 通过WebSocket广播消息
        from app import socketio
        socketio.emit('new_message', {
            'message': message.to_dict()
        }, room=room_id)
        
        return success_response({
            'message': message.to_dict(),
            'file_url': f"/uploads/images/{filename}"
        }, "图片上传成功")
        
    except Exception as e:
        # 清理文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        db.session.rollback()
        return error_response(f"上传失败: {str(e)}")

@bp.route('/upload/audio/<room_id>', methods=['POST'])
@require_auth()
def upload_audio(room_id):
    """上传语音"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    if 'file' not in request.files:
        return error_response("没有选择文件")
    
    file = request.files['file']
    if file.filename == '':
        return error_response("没有选择文件")
    
    if not allowed_file(file.filename, current_app.config['ALLOWED_AUDIO_EXTENSIONS']):
        return error_response("不支持的音频格式")
    
    try:
        # 生成安全的文件名
        filename = custom_secure_filename(file.filename)
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', filename)
        
        # 保存文件
        file.save(file_path)
        
        # 检查文件大小
        file_size = get_file_size(file_path)
        if file_size > current_app.config['MAX_CONTENT_LENGTH']:
            os.remove(file_path)
            return error_response("文件大小超过限制")
        
        # 获取音频时长（可选）
        duration = request.form.get('duration', type=int)
        
        # 创建消息记录
        message = Message(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            content=f"[语音] {duration}秒" if duration else "[语音消息]",
            message_type='audio',
            file_path=f"audio/{filename}",
            file_name=file.filename,
            file_size=file_size
        )
        
        if duration:
            message.set_metadata({'duration': duration})
        
        db.session.add(message)
        db.session.commit()
        
        # 通过WebSocket广播消息
        from app import socketio
        socketio.emit('new_message', {
            'message': message.to_dict()
        }, room=room_id)
        
        return success_response({
            'message': message.to_dict(),
            'file_url': f"/uploads/audio/{filename}"
        }, "语音上传成功")
        
    except Exception as e:
        # 清理文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        db.session.rollback()
        return error_response(f"上传失败: {str(e)}")

@bp.route('/download/<path:file_path>')
@require_auth()
def download_file(file_path):
    """下载文件"""
    # 安全检查，防止路径遍历攻击
    if '..' in file_path or file_path.startswith('/'):
        return error_response("非法文件路径", 400)
    
    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
    
    if not os.path.exists(full_path):
        return error_response("文件不存在", 404)
    
    try:
        return send_file(full_path)
    except Exception as e:
        return error_response(f"下载失败: {str(e)}")

@bp.route('/info/<message_id>')
@require_auth()
def get_file_info(message_id):
    """获取文件信息"""
    message = Message.query.get_or_404(message_id)
    
    if message.message_type not in ['image', 'audio']:
        return error_response("不是文件消息")
    
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=message.room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=message.room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], message.file_path)
    file_exists = os.path.exists(file_path)
    
    return success_response({
        'message_id': message_id,
        'file_name': message.file_name,
        'file_size': message.file_size,
        'file_path': message.file_path,
        'file_exists': file_exists,
        'message_type': message.message_type,
        'metadata': message.get_metadata(),
        'download_url': f"/api/files/download/{message.file_path}" if file_exists else None
    })
