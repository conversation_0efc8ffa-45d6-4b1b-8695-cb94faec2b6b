// 聊天管理

class ChatManager {
    constructor() {
        this.messages = new Map(); // roomId -> messages[]
        this.typingUsers = new Set();
        this.typingTimer = null;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听消息相关事件
        EventBus.on('message:received', (data) => {
            this.handleNewMessage(data);
        });
        
        EventBus.on('message:recalled', (data) => {
            this.handleMessageRecalled(data);
        });
        
        EventBus.on('message:reaction', (data) => {
            this.handleMessageReaction(data);
        });
        
        // 监听用户输入状态
        EventBus.on('user:typing', (data) => {
            this.handleUserTyping(data);
        });
        
        EventBus.on('user:stopTyping', (data) => {
            this.handleUserStopTyping(data);
        });
        
        // 监听输入框事件
        const messageInput = DOM.$('#message-input');
        if (messageInput) {
            let typingTimeout;
            
            messageInput.addEventListener('input', () => {
                this.handleInputChange();
                
                // 发送正在输入状态
                if (AppState.socket && AppState.currentRoom) {
                    AppState.socket.emit('typing', {
                        room_id: AppState.currentRoom.id
                    });
                }
                
                // 清除之前的定时器
                clearTimeout(typingTimeout);
                
                // 3秒后停止输入状态
                typingTimeout = setTimeout(() => {
                    if (AppState.socket && AppState.currentRoom) {
                        AppState.socket.emit('stop_typing', {
                            room_id: AppState.currentRoom.id
                        });
                    }
                }, 3000);
            });
        }
    }
    
    // 加载房间消息
    async loadRoomMessages(roomId, page = 1) {
        try {
            const response = await MessagesAPI.getMessages(roomId, page);
            const messages = response.messages || [];
            
            // 存储消息
            if (!this.messages.has(roomId)) {
                this.messages.set(roomId, []);
            }
            
            if (page === 1) {
                // 第一页，替换所有消息
                this.messages.set(roomId, messages.reverse());
            } else {
                // 后续页面，添加到开头
                const existingMessages = this.messages.get(roomId);
                this.messages.set(roomId, [...messages.reverse(), ...existingMessages]);
            }
            
            // 渲染消息
            this.renderMessages(roomId);
            
            // 滚动到底部（仅第一页）
            if (page === 1) {
                this.scrollToBottom();
            }
            
        } catch (error) {
            console.error('加载消息失败:', error);
            showNotification('加载消息失败', 'error');
        }
    }
    
    // 渲染消息
    renderMessages(roomId) {
        const messagesContainer = DOM.$('#messages-container');
        if (!messagesContainer || !this.messages.has(roomId)) return;
        
        const messages = this.messages.get(roomId);
        
        if (messages.length === 0) {
            messagesContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">💬</div>
                    <div class="empty-state-title">暂无消息</div>
                    <div class="empty-state-description">发送第一条消息开始聊天吧</div>
                </div>
            `;
            return;
        }
        
        const messagesHTML = messages.map(message => this.createMessageHTML(message)).join('');
        messagesContainer.innerHTML = messagesHTML + this.createTypingIndicatorHTML();
        
        // 添加消息事件监听器
        this.attachMessageEventListeners();
    }
    
    // 创建消息HTML
    createMessageHTML(message) {
        const isOwn = message.sender_id === AppState.user?.id;
        const senderName = message.sender_nickname || message.sender_username || '未知用户';
        const avatar = senderName.charAt(0).toUpperCase();
        const time = TimeUtils.formatDateTime(message.created_at);
        
        let contentHTML = '';
        
        switch (message.message_type) {
            case 'text':
                contentHTML = this.formatTextMessage(message.content);
                break;
            case 'image':
                contentHTML = this.createImageMessageHTML(message);
                break;
            case 'audio':
                contentHTML = this.createAudioMessageHTML(message);
                break;
            case 'file':
                contentHTML = this.createFileMessageHTML(message);
                break;
            case 'system':
                return this.createSystemMessageHTML(message);
            default:
                contentHTML = StringUtils.escapeHtml(message.content);
        }
        
        const actionsHTML = isOwn ? `
            <div class="message-actions">
                <button class="message-action-btn" onclick="window.chatManager.recallMessage('${message.id}')">撤回</button>
            </div>
        ` : `
            <div class="message-actions">
                <button class="message-action-btn" onclick="window.chatManager.replyToMessage('${message.id}')">回复</button>
                <button class="message-action-btn" onclick="window.chatManager.reactToMessage('${message.id}', '👍')">👍</button>
            </div>
        `;
        
        return `
            <div class="message-bubble ${isOwn ? 'own' : 'other'}" data-message-id="${message.id}">
                ${!isOwn ? `
                    <div class="message-header">
                        <div class="message-avatar">${avatar}</div>
                        <div class="message-sender">${StringUtils.escapeHtml(senderName)}</div>
                        <div class="message-time">${time}</div>
                    </div>
                ` : `
                    <div class="message-header">
                        <div class="message-time">${time}</div>
                    </div>
                `}
                <div class="message-content">
                    ${message.reply_to ? this.createReplyHTML(message.reply_to) : ''}
                    <div class="message-text">${contentHTML}</div>
                    ${message.reactions ? this.createReactionsHTML(message.reactions) : ''}
                </div>
                ${actionsHTML}
            </div>
        `;
    }
    
    // 格式化文本消息
    formatTextMessage(content) {
        let formatted = StringUtils.escapeHtml(content);
        formatted = StringUtils.formatLinks(formatted);
        formatted = StringUtils.formatMention(formatted);
        return formatted;
    }
    
    // 创建图片消息HTML
    createImageMessageHTML(message) {
        const imageUrl = FilesAPI.getDownloadUrl(message.file_path);
        return `
            <div class="message-image" onclick="window.chatManager.showImagePreview('${imageUrl}')">
                <img src="${imageUrl}" alt="图片" loading="lazy">
            </div>
        `;
    }
    
    // 创建语音消息HTML
    createAudioMessageHTML(message) {
        const audioUrl = FilesAPI.getDownloadUrl(message.file_path);
        const duration = message.extra_data?.duration || '未知';
        
        return `
            <div class="message-audio">
                <button class="audio-play-btn" onclick="window.chatManager.playAudio('${audioUrl}', this)">
                    ▶️
                </button>
                <div class="audio-waveform">
                    <div class="audio-progress"></div>
                </div>
                <div class="audio-duration">${duration}</div>
            </div>
        `;
    }
    
    // 创建文件消息HTML
    createFileMessageHTML(message) {
        const fileUrl = FilesAPI.getDownloadUrl(message.file_path);
        const fileName = message.extra_data?.original_name || '文件';
        const fileSize = message.extra_data?.size || 0;
        const icon = FileUtils.getFileIcon(fileName);
        
        return `
            <div class="message-file">
                <div class="file-icon">${icon}</div>
                <div class="file-info">
                    <div class="file-name">${StringUtils.escapeHtml(fileName)}</div>
                    <div class="file-size">${FileUtils.formatSize(fileSize)}</div>
                </div>
                <button class="file-download" onclick="window.chatManager.downloadFile('${fileUrl}', '${fileName}')">
                    下载
                </button>
            </div>
        `;
    }
    
    // 创建系统消息HTML
    createSystemMessageHTML(message) {
        return `
            <div class="system-message">
                ${StringUtils.escapeHtml(message.content)}
            </div>
        `;
    }
    
    // 创建回复HTML
    createReplyHTML(replyTo) {
        return `
            <div class="message-reply">
                <div class="reply-sender">${StringUtils.escapeHtml(replyTo.sender_name)}</div>
                <div class="reply-content">${StringUtils.truncate(replyTo.content, 50)}</div>
            </div>
        `;
    }
    
    // 创建反应HTML
    createReactionsHTML(reactions) {
        if (!reactions || Object.keys(reactions).length === 0) return '';
        
        const reactionsHTML = Object.entries(reactions).map(([emoji, users]) => {
            const isReacted = users.includes(AppState.user?.id);
            return `
                <button class="reaction-btn ${isReacted ? 'reacted' : ''}" 
                        onclick="window.chatManager.toggleReaction('${emoji}')">
                    ${emoji} ${users.length}
                </button>
            `;
        }).join('');
        
        return `<div class="message-reactions">${reactionsHTML}</div>`;
    }
    
    // 创建正在输入指示器HTML
    createTypingIndicatorHTML() {
        if (this.typingUsers.size === 0) return '';
        
        const typingUsersArray = Array.from(this.typingUsers);
        const firstUser = typingUsersArray[0];
        const avatar = firstUser.charAt(0).toUpperCase();
        
        return `
            <div class="typing-indicator">
                <div class="typing-avatar">${avatar}</div>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;
    }
    
    // 发送消息
    async sendMessage() {
        const messageInput = DOM.$('#message-input');
        if (!messageInput || !AppState.currentRoom) return;
        
        const content = messageInput.value.trim();
        if (!content) return;
        
        try {
            // 清空输入框
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // 发送消息
            await MessagesAPI.sendMessage(AppState.currentRoom.id, content);
            
            // 停止输入状态
            if (AppState.socket) {
                AppState.socket.emit('stop_typing', {
                    room_id: AppState.currentRoom.id
                });
            }
            
        } catch (error) {
            console.error('发送消息失败:', error);
            showNotification('发送消息失败', 'error');
            
            // 恢复输入框内容
            messageInput.value = content;
        }
    }
    
    // 处理新消息
    handleNewMessage(data) {
        const roomId = data.room_id;
        
        if (!this.messages.has(roomId)) {
            this.messages.set(roomId, []);
        }
        
        // 添加消息到列表
        this.messages.get(roomId).push(data.message);
        
        // 如果是当前房间，重新渲染
        if (AppState.currentRoom && AppState.currentRoom.id === roomId) {
            this.renderMessages(roomId);
            this.scrollToBottom();
        }
        
        // 播放提示音
        this.playNotificationSound();
        
        // 显示桌面通知
        if (data.message.sender_id !== AppState.user?.id) {
            this.showDesktopNotification(data.message);
        }
    }
    
    // 处理消息撤回
    handleMessageRecalled(data) {
        const roomId = data.room_id;
        const messageId = data.message_id;
        
        if (this.messages.has(roomId)) {
            const messages = this.messages.get(roomId);
            const messageIndex = messages.findIndex(m => m.id === messageId);
            
            if (messageIndex !== -1) {
                messages[messageIndex].content = '消息已撤回';
                messages[messageIndex].is_recalled = true;
                
                // 重新渲染
                if (AppState.currentRoom && AppState.currentRoom.id === roomId) {
                    this.renderMessages(roomId);
                }
            }
        }
    }
    
    // 处理消息反应
    handleMessageReaction(data) {
        const roomId = data.room_id;
        const messageId = data.message_id;
        
        if (this.messages.has(roomId)) {
            const messages = this.messages.get(roomId);
            const message = messages.find(m => m.id === messageId);
            
            if (message) {
                message.reactions = data.reactions;
                
                // 重新渲染
                if (AppState.currentRoom && AppState.currentRoom.id === roomId) {
                    this.renderMessages(roomId);
                }
            }
        }
    }
    
    // 处理用户正在输入
    handleUserTyping(data) {
        if (AppState.currentRoom && data.room_id === AppState.currentRoom.id) {
            this.typingUsers.add(data.username);
            this.updateTypingIndicator();
        }
    }
    
    // 处理用户停止输入
    handleUserStopTyping(data) {
        if (AppState.currentRoom && data.room_id === AppState.currentRoom.id) {
            this.typingUsers.delete(data.username);
            this.updateTypingIndicator();
        }
    }
    
    // 更新正在输入指示器
    updateTypingIndicator() {
        if (AppState.currentRoom) {
            this.renderMessages(AppState.currentRoom.id);
        }
    }
    
    // 处理输入变化
    handleInputChange() {
        const messageInput = DOM.$('#message-input');
        const sendBtn = DOM.$('.send-btn');
        
        if (messageInput && sendBtn) {
            const hasContent = messageInput.value.trim().length > 0;
            sendBtn.disabled = !hasContent;
        }
    }
    
    // 撤回消息
    async recallMessage(messageId) {
        try {
            await MessagesAPI.recallMessage(messageId);
            showNotification('消息已撤回', 'success');
        } catch (error) {
            console.error('撤回消息失败:', error);
            showNotification('撤回消息失败', 'error');
        }
    }
    
    // 回复消息
    replyToMessage(messageId) {
        // 实现回复功能
        showNotification('回复功能开发中', 'info');
    }
    
    // 对消息做出反应
    async reactToMessage(messageId, reaction) {
        try {
            await MessagesAPI.reactToMessage(messageId, reaction);
        } catch (error) {
            console.error('消息反应失败:', error);
            showNotification('操作失败', 'error');
        }
    }
    
    // 添加系统消息
    addSystemMessage(content) {
        if (!AppState.currentRoom) return;
        
        const systemMessage = {
            id: StringUtils.generateId(),
            content,
            message_type: 'system',
            created_at: new Date().toISOString()
        };
        
        const roomId = AppState.currentRoom.id;
        if (!this.messages.has(roomId)) {
            this.messages.set(roomId, []);
        }
        
        this.messages.get(roomId).push(systemMessage);
        this.renderMessages(roomId);
        this.scrollToBottom();
    }
    
    // 滚动到底部
    scrollToBottom() {
        const messagesContainer = DOM.$('#messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }
    
    // 播放通知音
    playNotificationSound() {
        const settings = Storage.get('settings', {});
        if (settings.soundEnabled !== false) {
            // 创建简单的提示音
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        }
    }
    
    // 显示桌面通知
    showDesktopNotification(message) {
        const settings = Storage.get('settings', {});
        if (settings.desktopNotifications && 'Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification(`${message.sender_nickname || message.sender_username}`, {
                    body: message.content,
                    icon: '/static/images/logo.png'
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        this.showDesktopNotification(message);
                    }
                });
            }
        }
    }
    
    // 附加消息事件监听器
    attachMessageEventListeners() {
        // 这里可以添加更多的消息交互事件监听器
    }
}

// 全局函数
window.showFileUpload = function() {
    if (window.fileManager) {
        window.fileManager.showFileUploadDialog();
    }
};

window.showEmoji = function() {
    showNotification('表情功能开发中', 'info');
};

// 初始化聊天管理器
document.addEventListener('DOMContentLoaded', () => {
    window.chatManager = new ChatManager();
});
