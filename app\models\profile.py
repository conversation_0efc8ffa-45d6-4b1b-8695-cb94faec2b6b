from . import db, generate_uuid
from datetime import datetime
import json

class ProfileCategory(db.Model):
    __tablename__ = 'profile_categories'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    name = db.Column(db.String(50), unique=True, nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    fields = db.relationship('ProfileField', backref='category', lazy='dynamic', cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'is_active': self.is_active,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'fields': [field.to_dict() for field in self.fields.filter_by(is_active=True).order_by(ProfileField.sort_order)]
        }

class ProfileField(db.Model):
    __tablename__ = 'profile_fields'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    category_id = db.Column(db.String(36), db.ForeignKey('profile_categories.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    field_type = db.Column(db.String(20), default='single_choice')  # single_choice, multiple_choice, text, number
    options = db.Column(db.Text, nullable=True)  # JSON格式存储选项
    is_required = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    max_selections = db.Column(db.Integer, nullable=True)  # 多选时的最大选择数
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    user_profiles = db.relationship('UserProfile', backref='field', lazy='dynamic')
    
    # 确保同一分类下字段名唯一
    __table_args__ = (
        db.UniqueConstraint('category_id', 'name', name='unique_category_field'),
    )
    
    def set_options(self, options_list):
        self.options = json.dumps(options_list) if options_list else None
    
    def get_options(self):
        return json.loads(self.options) if self.options else []
    
    def to_dict(self):
        return {
            'id': self.id,
            'category_id': self.category_id,
            'name': self.name,
            'display_name': self.display_name,
            'field_type': self.field_type,
            'options': self.get_options(),
            'is_required': self.is_required,
            'is_active': self.is_active,
            'max_selections': self.max_selections,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class UserProfile(db.Model):
    __tablename__ = 'user_profiles'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)
    anonymous_user_id = db.Column(db.String(36), db.ForeignKey('anonymous_users.id'), nullable=True)
    field_id = db.Column(db.String(36), db.ForeignKey('profile_fields.id'), nullable=False)
    value = db.Column(db.Text, nullable=False)  # JSON格式存储值
    is_hidden = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    reveals = db.relationship('ProfileReveal', backref='profile', lazy='dynamic')
    
    # 确保用户或匿名用户只能有一个，且同一用户对同一字段只能有一个配置
    __table_args__ = (
        db.CheckConstraint('(user_id IS NOT NULL) != (anonymous_user_id IS NOT NULL)', 
                          name='check_profile_user_or_anonymous'),
        db.UniqueConstraint('user_id', 'field_id', name='unique_user_field'),
        db.UniqueConstraint('anonymous_user_id', 'field_id', name='unique_anonymous_field')
    )
    
    def get_user_id(self):
        return self.user_id or self.anonymous_user_id
    
    def set_value(self, value):
        if isinstance(value, (list, dict)):
            self.value = json.dumps(value)
        else:
            self.value = str(value)
    
    def get_value(self):
        try:
            return json.loads(self.value)
        except:
            return self.value
    
    def get_display_value(self):
        value = self.get_value()
        if isinstance(value, list):
            return ', '.join(value)
        return str(value)
    
    def to_dict(self, show_hidden=False):
        if self.is_hidden and not show_hidden:
            return {
                'id': self.id,
                'field_id': self.field_id,
                'field_name': self.field.display_name,
                'category_name': self.field.category.display_name,
                'is_hidden': True,
                'value': '***隐藏***'
            }
        
        return {
            'id': self.id,
            'user_id': self.get_user_id(),
            'field_id': self.field_id,
            'field_name': self.field.display_name,
            'category_name': self.field.category.display_name,
            'value': self.get_value(),
            'display_value': self.get_display_value(),
            'is_hidden': self.is_hidden,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ProfileReveal(db.Model):
    __tablename__ = 'profile_reveals'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    profile_id = db.Column(db.String(36), db.ForeignKey('user_profiles.id'), nullable=False)
    room_id = db.Column(db.String(36), db.ForeignKey('rooms.id'), nullable=False)
    revealed_at = db.Column(db.DateTime, default=datetime.utcnow)
    message_id = db.Column(db.String(36), db.ForeignKey('messages.id'), nullable=True)  # 关联的揭露消息
    
    # 确保同一个资料在同一个房间只能揭露一次
    __table_args__ = (
        db.UniqueConstraint('profile_id', 'room_id', name='unique_profile_room_reveal'),
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'profile_id': self.profile_id,
            'room_id': self.room_id,
            'revealed_at': self.revealed_at.isoformat() if self.revealed_at else None,
            'message_id': self.message_id,
            'profile': self.profile.to_dict(show_hidden=True)
        }
