// 问卷系统

class QuestionnaireManager {
    constructor() {
        this.questionnaires = [];
        this.responses = new Map();
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听侧边栏视图切换
        EventBus.on('sidebar:viewChanged', (view) => {
            if (view === 'questionnaire') {
                this.loadQuestionnaires();
            }
        });
        
        // 监听问卷发送事件
        EventBus.on('questionnaire:sent', (data) => {
            this.handleQuestionnaireSent(data);
        });
    }
    
    // 加载问卷列表
    async loadQuestionnaires() {
        try {
            const response = await QuestionnairesAPI.getQuestionnaires();
            this.questionnaires = response.questionnaires || [];
            
            this.renderQuestionnaires();
            
        } catch (error) {
            console.error('加载问卷列表失败:', error);
            showNotification('加载问卷列表失败', 'error');
        }
    }
    
    // 渲染问卷列表
    renderQuestionnaires() {
        const questionnaireContent = DOM.$('#questionnaire-content');
        if (!questionnaireContent) return;
        
        if (this.questionnaires.length === 0) {
            questionnaireContent.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📋</div>
                    <div class="empty-state-title">暂无问卷</div>
                    <div class="empty-state-description">
                        ${AppState.isAdmin ? '创建第一个问卷吧' : '等待管理员创建问卷'}
                    </div>
                    ${AppState.isAdmin ? '<button class="btn btn-primary" onclick="window.questionnaireManager.showCreateDialog()">创建问卷</button>' : ''}
                </div>
            `;
            return;
        }
        
        const questionnairesHTML = this.questionnaires.map(questionnaire => {
            const status = this.getQuestionnaireStatus(questionnaire);
            return `
                <div class="questionnaire-item">
                    <div class="questionnaire-header">
                        <h4>${StringUtils.escapeHtml(questionnaire.title)}</h4>
                        <span class="questionnaire-status ${status.class}">${status.text}</span>
                    </div>
                    <div class="questionnaire-description">
                        ${StringUtils.escapeHtml(questionnaire.description || '')}
                    </div>
                    <div class="questionnaire-meta">
                        <span>创建时间: ${TimeUtils.formatDateTime(questionnaire.created_at)}</span>
                        <span>回答数: ${questionnaire.response_count || 0}</span>
                    </div>
                    <div class="questionnaire-actions">
                        ${this.getQuestionnaireActions(questionnaire)}
                    </div>
                </div>
            `;
        }).join('');
        
        questionnaireContent.innerHTML = questionnairesHTML;
    }
    
    // 获取问卷状态
    getQuestionnaireStatus(questionnaire) {
        if (!questionnaire.is_active) {
            return { class: 'inactive', text: '已停用' };
        }
        
        if (questionnaire.end_time && new Date(questionnaire.end_time) < new Date()) {
            return { class: 'expired', text: '已过期' };
        }
        
        return { class: 'active', text: '进行中' };
    }
    
    // 获取问卷操作按钮
    getQuestionnaireActions(questionnaire) {
        const actions = [];
        
        // 查看问卷
        actions.push(`<button class="btn btn-secondary btn-sm" onclick="window.questionnaireManager.viewQuestionnaire('${questionnaire.id}')">查看</button>`);
        
        // 填写问卷（如果未填写且问卷有效）
        if (questionnaire.is_active && !questionnaire.has_responded) {
            actions.push(`<button class="btn btn-primary btn-sm" onclick="window.questionnaireManager.fillQuestionnaire('${questionnaire.id}')">填写</button>`);
        }
        
        // 管理员操作
        if (AppState.isAdmin) {
            actions.push(`<button class="btn btn-info btn-sm" onclick="window.questionnaireManager.viewResponses('${questionnaire.id}')">查看回答</button>`);
            actions.push(`<button class="btn btn-warning btn-sm" onclick="window.questionnaireManager.sendToRoom('${questionnaire.id}')">发送到房间</button>`);
        }
        
        return actions.join(' ');
    }
    
    // 显示创建问卷对话框
    showCreateDialog() {
        const content = `
            <div class="form-group">
                <label class="form-label">问卷标题</label>
                <input type="text" id="questionnaire-title" class="form-control" placeholder="输入问卷标题" maxlength="100">
            </div>
            <div class="form-group">
                <label class="form-label">问卷描述</label>
                <textarea id="questionnaire-description" class="form-textarea" placeholder="输入问卷描述（可选）" maxlength="500"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">问题列表</label>
                <div id="questions-container">
                    <div class="question-item">
                        <input type="text" class="form-control question-text" placeholder="输入问题" maxlength="200">
                        <select class="form-select question-type">
                            <option value="text">文本</option>
                            <option value="single_choice">单选</option>
                            <option value="multiple_choice">多选</option>
                            <option value="rating">评分</option>
                        </select>
                        <button type="button" class="btn-icon" onclick="this.parentElement.remove()">🗑️</button>
                    </div>
                </div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="window.questionnaireManager.addQuestion()">添加问题</button>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">结束时间</label>
                    <input type="datetime-local" id="questionnaire-end-time" class="form-control">
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="questionnaire-anonymous" checked>
                        <label for="questionnaire-anonymous">匿名回答</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="window.questionnaireManager.createQuestionnaire()">创建</button>
            </div>
        `;
        
        window.app.showModal(content, '创建问卷');
    }
    
    // 添加问题
    addQuestion() {
        const container = DOM.$('#questions-container');
        const questionItem = DOM.create('div', {
            className: 'question-item',
            innerHTML: `
                <input type="text" class="form-control question-text" placeholder="输入问题" maxlength="200">
                <select class="form-select question-type">
                    <option value="text">文本</option>
                    <option value="single_choice">单选</option>
                    <option value="multiple_choice">多选</option>
                    <option value="rating">评分</option>
                </select>
                <button type="button" class="btn-icon" onclick="this.parentElement.remove()">🗑️</button>
            `
        });
        
        container.appendChild(questionItem);
    }
    
    // 创建问卷
    async createQuestionnaire() {
        const title = DOM.$('#questionnaire-title').value.trim();
        const description = DOM.$('#questionnaire-description').value.trim();
        const endTime = DOM.$('#questionnaire-end-time').value;
        const isAnonymous = DOM.$('#questionnaire-anonymous').checked;
        
        if (!title) {
            showNotification('请输入问卷标题', 'error');
            return;
        }
        
        // 收集问题
        const questionItems = DOM.$$('.question-item');
        const questions = [];
        
        for (const item of questionItems) {
            const text = item.querySelector('.question-text').value.trim();
            const type = item.querySelector('.question-type').value;
            
            if (text) {
                questions.push({ text, type });
            }
        }
        
        if (questions.length === 0) {
            showNotification('请至少添加一个问题', 'error');
            return;
        }
        
        try {
            const questionnaireData = {
                title,
                description,
                questions,
                end_time: endTime || null,
                is_anonymous: isAnonymous
            };
            
            await QuestionnairesAPI.createQuestionnaire(questionnaireData);
            
            window.app.closeModal();
            showNotification('问卷创建成功', 'success');
            
            // 重新加载问卷列表
            await this.loadQuestionnaires();
            
        } catch (error) {
            console.error('创建问卷失败:', error);
            showNotification('创建问卷失败', 'error');
        }
    }
    
    // 查看问卷
    async viewQuestionnaire(questionnaireId) {
        try {
            const response = await QuestionnairesAPI.getQuestionnaire(questionnaireId);
            const questionnaire = response.questionnaire;
            
            this.showQuestionnaireModal(questionnaire, 'view');
            
        } catch (error) {
            console.error('获取问卷详情失败:', error);
            showNotification('获取问卷详情失败', 'error');
        }
    }
    
    // 填写问卷
    async fillQuestionnaire(questionnaireId) {
        try {
            const response = await QuestionnairesAPI.getQuestionnaire(questionnaireId);
            const questionnaire = response.questionnaire;
            
            this.showQuestionnaireModal(questionnaire, 'fill');
            
        } catch (error) {
            console.error('获取问卷详情失败:', error);
            showNotification('获取问卷详情失败', 'error');
        }
    }
    
    // 显示问卷模态框
    showQuestionnaireModal(questionnaire, mode) {
        const isViewMode = mode === 'view';
        const isFillMode = mode === 'fill';
        
        const questionsHTML = questionnaire.questions.map((question, index) => {
            return `
                <div class="question-block">
                    <div class="question-title">${index + 1}. ${StringUtils.escapeHtml(question.text)}</div>
                    ${this.renderQuestionInput(question, index, isFillMode)}
                </div>
            `;
        }).join('');
        
        const content = `
            <div class="questionnaire-modal">
                <div class="questionnaire-info">
                    <h3>${StringUtils.escapeHtml(questionnaire.title)}</h3>
                    ${questionnaire.description ? `<p>${StringUtils.escapeHtml(questionnaire.description)}</p>` : ''}
                </div>
                <div class="questions-list">
                    ${questionsHTML}
                </div>
                ${isFillMode ? `
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="window.questionnaireManager.submitResponse('${questionnaire.id}')">提交</button>
                    </div>
                ` : ''}
            </div>
        `;
        
        window.app.showModal(content, isViewMode ? '查看问卷' : '填写问卷');
    }
    
    // 渲染问题输入
    renderQuestionInput(question, index, isEditable) {
        if (!isEditable) {
            return `<div class="question-preview">预览模式</div>`;
        }
        
        switch (question.type) {
            case 'text':
                return `<textarea class="form-textarea question-answer" data-question-index="${index}" placeholder="请输入您的回答"></textarea>`;
            
            case 'single_choice':
                const singleOptions = question.options || ['选项1', '选项2', '选项3'];
                return singleOptions.map((option, optIndex) => `
                    <div class="form-radio">
                        <input type="radio" name="question_${index}" value="${optIndex}" id="q${index}_${optIndex}">
                        <label for="q${index}_${optIndex}">${StringUtils.escapeHtml(option)}</label>
                    </div>
                `).join('');
            
            case 'multiple_choice':
                const multiOptions = question.options || ['选项1', '选项2', '选项3'];
                return multiOptions.map((option, optIndex) => `
                    <div class="form-checkbox">
                        <input type="checkbox" name="question_${index}" value="${optIndex}" id="q${index}_${optIndex}">
                        <label for="q${index}_${optIndex}">${StringUtils.escapeHtml(option)}</label>
                    </div>
                `).join('');
            
            case 'rating':
                return `
                    <div class="rating-input">
                        ${[1, 2, 3, 4, 5].map(rating => `
                            <input type="radio" name="question_${index}" value="${rating}" id="q${index}_${rating}">
                            <label for="q${index}_${rating}">⭐</label>
                        `).join('')}
                    </div>
                `;
            
            default:
                return `<input type="text" class="form-control question-answer" data-question-index="${index}" placeholder="请输入您的回答">`;
        }
    }
    
    // 提交问卷回答
    async submitResponse(questionnaireId) {
        const answers = [];
        const questionBlocks = DOM.$$('.question-block');
        
        questionBlocks.forEach((block, index) => {
            const textAnswer = block.querySelector('.question-answer');
            if (textAnswer) {
                answers.push(textAnswer.value.trim());
                return;
            }
            
            const radioInputs = block.querySelectorAll(`input[name="question_${index}"]:checked`);
            if (radioInputs.length > 0) {
                answers.push(radioInputs[0].value);
                return;
            }
            
            const checkboxInputs = block.querySelectorAll(`input[name="question_${index}"]:checked`);
            if (checkboxInputs.length > 0) {
                answers.push(Array.from(checkboxInputs).map(cb => cb.value));
                return;
            }
            
            answers.push('');
        });
        
        try {
            await QuestionnairesAPI.submitResponse(questionnaireId, answers);
            
            window.app.closeModal();
            showNotification('问卷提交成功', 'success');
            
            // 重新加载问卷列表
            await this.loadQuestionnaires();
            
        } catch (error) {
            console.error('提交问卷失败:', error);
            showNotification('提交问卷失败', 'error');
        }
    }
    
    // 发送问卷到房间
    async sendToRoom(questionnaireId) {
        if (!AppState.currentRoom) {
            showNotification('请先加入一个房间', 'error');
            return;
        }
        
        try {
            await QuestionnairesAPI.sendToRoom(questionnaireId, AppState.currentRoom.id);
            showNotification('问卷已发送到房间', 'success');
            
        } catch (error) {
            console.error('发送问卷失败:', error);
            showNotification('发送问卷失败', 'error');
        }
    }
    
    // 查看问卷回答
    async viewResponses(questionnaireId) {
        try {
            const response = await QuestionnairesAPI.getResponses(questionnaireId);
            const responses = response.responses || [];
            
            this.showResponsesModal(responses);
            
        } catch (error) {
            console.error('获取问卷回答失败:', error);
            showNotification('获取问卷回答失败', 'error');
        }
    }
    
    // 显示回答模态框
    showResponsesModal(responses) {
        if (responses.length === 0) {
            const content = `
                <div class="empty-state">
                    <div class="empty-state-icon">📝</div>
                    <div class="empty-state-title">暂无回答</div>
                    <div class="empty-state-description">还没有人填写这个问卷</div>
                </div>
            `;
            window.app.showModal(content, '问卷回答');
            return;
        }
        
        const responsesHTML = responses.map((response, index) => `
            <div class="response-item">
                <div class="response-header">
                    <span>回答 ${index + 1}</span>
                    <span>${TimeUtils.formatDateTime(response.created_at)}</span>
                </div>
                <div class="response-answers">
                    ${response.answers.map((answer, qIndex) => `
                        <div class="answer-item">
                            <div class="answer-question">问题 ${qIndex + 1}</div>
                            <div class="answer-text">${StringUtils.escapeHtml(answer || '未回答')}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
        
        const content = `
            <div class="responses-modal">
                <div class="responses-summary">
                    <h4>共收到 ${responses.length} 份回答</h4>
                </div>
                <div class="responses-list">
                    ${responsesHTML}
                </div>
            </div>
        `;
        
        window.app.showModal(content, '问卷回答');
    }
    
    // 处理问卷发送事件
    handleQuestionnaireSent(data) {
        if (AppState.currentRoom && data.room_id === AppState.currentRoom.id) {
            showNotification(`收到新问卷: ${data.questionnaire_title}`, 'info');
            
            // 在聊天中显示问卷消息
            if (window.chatManager) {
                window.chatManager.addSystemMessage(`📋 收到问卷: ${data.questionnaire_title}`);
            }
        }
    }
}

// 全局函数
window.createQuestionnaire = function() {
    if (AppState.isAdmin) {
        window.questionnaireManager.showCreateDialog();
    } else {
        showNotification('只有管理员可以创建问卷', 'error');
    }
};

// 初始化问卷管理器
document.addEventListener('DOMContentLoaded', () => {
    window.questionnaireManager = new QuestionnaireManager();
});
