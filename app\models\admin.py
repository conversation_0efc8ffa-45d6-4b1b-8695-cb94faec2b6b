from . import db, generate_uuid
from datetime import datetime
import json

class AdminLog(db.Model):
    __tablename__ = 'admin_logs'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    admin_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    target_type = db.Column(db.String(50), nullable=True)  # user, room, message, invite_code等
    target_id = db.Column(db.String(36), nullable=True)
    details = db.Column(db.Text, nullable=True)  # JSON格式存储详细信息
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    # 关系
    admin = db.relationship('User', backref='admin_logs')
    
    def set_details(self, details):
        self.details = json.dumps(details) if details else None
    
    def get_details(self):
        return json.loads(self.details) if self.details else {}
    
    def to_dict(self):
        return {
            'id': self.id,
            'admin_id': self.admin_id,
            'admin_name': self.admin.username if self.admin else '未知管理员',
            'action': self.action,
            'target_type': self.target_type,
            'target_id': self.target_id,
            'details': self.get_details(),
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @staticmethod
    def log_action(admin_id, action, target_type=None, target_id=None, details=None, ip_address=None, user_agent=None):
        """记录管理员操作日志"""
        log = AdminLog(
            admin_id=admin_id,
            action=action,
            target_type=target_type,
            target_id=target_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        if details:
            log.set_details(details)
        
        db.session.add(log)
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"Failed to log admin action: {e}")
        
        return log
