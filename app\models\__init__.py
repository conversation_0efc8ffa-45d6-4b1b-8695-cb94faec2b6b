from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

db = SQLAlchemy()

def generate_uuid():
    return str(uuid.uuid4())

# 导入所有模型
from .user import User, AnonymousUser, InviteCode
from .room import Room, RoomMember, RoomInviteLink
from .message import Message, MessageReaction
from .profile import ProfileCategory, ProfileField, UserProfile, ProfileReveal
from .questionnaire import Questionnaire, Question, QuestionOption, QuestionnaireResponse, QuestionResponse
from .admin import AdminLog
