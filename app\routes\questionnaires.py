from flask import Blueprint, request
from app.models import db
from app.models.questionnaire import (Questionnaire, Question, QuestionOption, 
                                     QuestionnaireResponse, QuestionResponse)
from app.models.room import RoomMember
from app.models.message import Message
from app.utils.helpers import (success_response, error_response, require_auth,
                              validate_request_data, paginate_query)
import uuid

bp = Blueprint('questionnaires', __name__)

@bp.route('/', methods=['GET'])
@require_auth()
def list_questionnaires():
    """获取问卷列表"""
    user = request.current_user
    user_type = request.user_type
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    is_template = request.args.get('template', type=bool)
    
    query = Questionnaire.query.filter_by(is_active=True)
    
    if is_template is not None:
        query = query.filter_by(is_template=is_template)
    else:
        # 默认只显示用户自己创建的问卷和模板问卷
        if user_type == 'user':
            query = query.filter(
                (Questionnaire.created_by == user.id) | 
                (Questionnaire.is_template == True)
            )
        else:
            query = query.filter(
                (Questionnaire.anonymous_created_by == user.id) | 
                (Questionnaire.is_template == True)
            )
    
    query = query.order_by(Questionnaire.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'questionnaires': [q.to_dict(include_questions=False) for q in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/', methods=['POST'])
@require_auth()
def create_questionnaire():
    """创建问卷"""
    user = request.current_user
    user_type = request.user_type
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['title', 'questions'])
    if not valid:
        return error_response(error_msg)
    
    title = data['title'].strip()
    description = data.get('description', '').strip()
    questions_data = data['questions']
    is_template = data.get('is_template', False)
    
    if len(title) < 2 or len(title) > 200:
        return error_response("问卷标题长度应在2-200字符之间")
    
    if not questions_data or len(questions_data) == 0:
        return error_response("问卷至少需要一个问题")
    
    if len(questions_data) > 50:
        return error_response("问卷最多支持50个问题")
    
    try:
        # 创建问卷
        questionnaire = Questionnaire(
            title=title,
            description=description,
            created_by=user.id if user_type == 'user' else None,
            anonymous_created_by=user.id if user_type == 'anonymous' else None,
            is_template=is_template
        )
        db.session.add(questionnaire)
        db.session.flush()  # 获取问卷ID
        
        # 创建问题
        for i, q_data in enumerate(questions_data):
            if not q_data.get('question_text'):
                return error_response(f"第{i+1}个问题的内容不能为空")
            
            question = Question(
                questionnaire_id=questionnaire.id,
                question_text=q_data['question_text'].strip(),
                question_type=q_data.get('question_type', 'single_choice'),
                is_required=q_data.get('is_required', True),
                sort_order=i
            )
            
            if q_data.get('metadata'):
                question.set_metadata(q_data['metadata'])
            
            db.session.add(question)
            db.session.flush()  # 获取问题ID
            
            # 创建选项（如果是选择题）
            if question.question_type in ['single_choice', 'multiple_choice']:
                options_data = q_data.get('options', [])
                if not options_data:
                    return error_response(f"第{i+1}个问题缺少选项")
                
                for j, option_text in enumerate(options_data):
                    if not option_text.strip():
                        continue
                    
                    option = QuestionOption(
                        question_id=question.id,
                        option_text=option_text.strip(),
                        sort_order=j
                    )
                    db.session.add(option)
        
        db.session.commit()
        
        return success_response({
            'questionnaire': questionnaire.to_dict()
        }, "问卷创建成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建问卷失败: {str(e)}")

@bp.route('/<questionnaire_id>', methods=['GET'])
@require_auth()
def get_questionnaire(questionnaire_id):
    """获取问卷详情"""
    questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
    
    if not questionnaire.is_active:
        return error_response("问卷不存在或已被禁用", 404)
    
    return success_response({
        'questionnaire': questionnaire.to_dict()
    })

@bp.route('/<questionnaire_id>/send/<room_id>', methods=['POST'])
@require_auth()
def send_questionnaire(questionnaire_id, room_id):
    """在房间中发送问卷"""
    user = request.current_user
    user_type = request.user_type
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
    
    if not questionnaire.is_active:
        return error_response("问卷不存在或已被禁用", 404)
    
    try:
        # 生成问卷实例ID
        instance_id = str(uuid.uuid4())
        
        # 创建消息
        message = Message(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            content=f"[问卷] {questionnaire.title}",
            message_type='questionnaire'
        )
        message.set_metadata({
            'questionnaire_id': questionnaire_id,
            'instance_id': instance_id,
            'title': questionnaire.title,
            'description': questionnaire.description,
            'question_count': questionnaire.questions.count()
        })
        
        db.session.add(message)
        db.session.commit()
        
        # 通过WebSocket广播消息
        from app import socketio
        socketio.emit('new_message', {
            'message': message.to_dict()
        }, room=room_id)
        
        return success_response({
            'message': message.to_dict(),
            'instance_id': instance_id
        }, "问卷发送成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"发送问卷失败: {str(e)}")

@bp.route('/respond/<instance_id>', methods=['POST'])
@require_auth()
def respond_questionnaire(instance_id):
    """回答问卷"""
    user = request.current_user
    user_type = request.user_type
    
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['questionnaire_id', 'room_id', 'responses'])
    if not valid:
        return error_response(error_msg)
    
    questionnaire_id = data['questionnaire_id']
    room_id = data['room_id']
    responses_data = data['responses']
    
    # 检查是否是房间成员
    member = None
    if user_type == 'user':
        member = RoomMember.query.filter_by(
            room_id=room_id, user_id=user.id, is_active=True
        ).first()
    else:
        member = RoomMember.query.filter_by(
            room_id=room_id, anonymous_user_id=user.id, is_active=True
        ).first()
    
    if not member:
        return error_response("您不是房间成员", 403)
    
    questionnaire = Questionnaire.query.get_or_404(questionnaire_id)
    
    # 检查是否已经回答过
    existing_response = None
    if user_type == 'user':
        existing_response = QuestionnaireResponse.query.filter_by(
            instance_id=instance_id, user_id=user.id
        ).first()
    else:
        existing_response = QuestionnaireResponse.query.filter_by(
            instance_id=instance_id, anonymous_user_id=user.id
        ).first()
    
    if existing_response:
        return error_response("您已经回答过这个问卷实例")
    
    try:
        # 创建问卷回答
        questionnaire_response = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            instance_id=instance_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            room_id=room_id
        )
        db.session.add(questionnaire_response)
        db.session.flush()  # 获取回答ID
        
        # 创建问题回答
        for question_id, answer in responses_data.items():
            question = Question.query.get(question_id)
            if not question or question.questionnaire_id != questionnaire_id:
                continue
            
            # 验证必填问题
            if question.is_required and (not answer or answer == ''):
                return error_response(f"问题 '{question.question_text}' 是必填的")
            
            if answer:  # 只保存有答案的问题
                question_response = QuestionResponse(
                    questionnaire_response_id=questionnaire_response.id,
                    question_id=question_id
                )
                question_response.set_answer(answer)
                db.session.add(question_response)
        
        # 标记为完成
        questionnaire_response.complete()
        
        db.session.commit()
        
        # 创建完成通知消息
        message = Message(
            room_id=room_id,
            user_id=user.id if user_type == 'user' else None,
            anonymous_user_id=user.id if user_type == 'anonymous' else None,
            content=f"已完成问卷《{questionnaire.title}》的回答",
            message_type='questionnaire_response'
        )
        message.set_metadata({
            'questionnaire_id': questionnaire_id,
            'instance_id': instance_id,
            'response_id': questionnaire_response.id,
            'title': questionnaire.title
        })
        
        db.session.add(message)
        db.session.commit()
        
        # 通过WebSocket广播完成消息
        from app import socketio
        socketio.emit('new_message', {
            'message': message.to_dict()
        }, room=room_id)
        
        return success_response({
            'response': questionnaire_response.to_dict(),
            'message': message.to_dict()
        }, "问卷回答提交成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"提交回答失败: {str(e)}")

@bp.route('/responses/<instance_id>', methods=['GET'])
@require_auth()
def get_responses(instance_id):
    """获取问卷实例的所有回答"""
    responses = QuestionnaireResponse.query.filter_by(
        instance_id=instance_id, is_completed=True
    ).all()
    
    return success_response({
        'responses': [resp.to_dict(include_responses=True) for resp in responses],
        'total': len(responses)
    })

@bp.route('/response/<response_id>', methods=['GET'])
@require_auth()
def get_response_detail(response_id):
    """获取问卷回答详情"""
    response = QuestionnaireResponse.query.get_or_404(response_id)
    
    return success_response({
        'response': response.to_dict(include_responses=True)
    })
