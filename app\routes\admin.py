from flask import Blueprint, request, jsonify, make_response
from app.models import db
from app.models.user import User, InviteCode
from app.models.room import Room, RoomMember
from app.models.message import Message
from app.models.admin import AdminLog
from app.utils.helpers import (success_response, error_response, require_admin,
                              validate_request_data, paginate_query, log_admin_action,
                              generate_invite_code)
from datetime import datetime
import csv
import io

bp = Blueprint('admin', __name__)

@bp.route('/dashboard', methods=['GET'])
@require_admin()
def dashboard():
    """管理员仪表板"""
    # 统计数据
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    total_rooms = Room.query.filter_by(is_active=True).count()
    total_messages = Message.query.count()
    
    # 最近7天的活跃用户
    from datetime import timedelta
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_active_users = User.query.filter(User.last_login >= week_ago).count()
    
    return success_response({
        'stats': {
            'total_users': total_users,
            'active_users': active_users,
            'total_rooms': total_rooms,
            'total_messages': total_messages,
            'recent_active_users': recent_active_users
        }
    })

@bp.route('/users', methods=['GET'])
@require_admin()
def list_users():
    """获取用户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '').strip()
    
    query = User.query
    
    if search:
        query = query.filter(User.username.contains(search))
    
    query = query.order_by(User.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'users': [user.to_dict(include_sensitive=True) for user in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/users/<user_id>/toggle-room-permission', methods=['POST'])
@require_admin()
def toggle_user_room_permission(user_id):
    """切换用户创建房间权限"""
    user = User.query.get_or_404(user_id)
    
    user.can_create_room = not user.can_create_room
    db.session.commit()
    
    log_admin_action(
        'toggle_room_permission',
        'user',
        user_id,
        {'can_create_room': user.can_create_room}
    )
    
    return success_response({
        'user': user.to_dict(),
        'can_create_room': user.can_create_room
    }, f"用户创建房间权限已{'开启' if user.can_create_room else '关闭'}")

@bp.route('/users/<user_id>/toggle-active', methods=['POST'])
@require_admin()
def toggle_user_active(user_id):
    """切换用户激活状态"""
    user = User.query.get_or_404(user_id)
    
    if user.is_admin:
        return error_response("不能禁用管理员账户")
    
    user.is_active = not user.is_active
    db.session.commit()
    
    log_admin_action(
        'toggle_user_active',
        'user',
        user_id,
        {'is_active': user.is_active}
    )
    
    return success_response({
        'user': user.to_dict(),
        'is_active': user.is_active
    }, f"用户已{'激活' if user.is_active else '禁用'}")

@bp.route('/invite-codes', methods=['GET'])
@require_admin()
def list_invite_codes():
    """获取邀请码列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    query = InviteCode.query.order_by(InviteCode.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'invite_codes': [code.to_dict() for code in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/invite-codes', methods=['POST'])
@require_admin()
def create_invite_codes():
    """批量创建邀请码"""
    data = request.get_json()
    
    count = data.get('count', 1)
    note = data.get('note', '').strip()
    max_uses = data.get('max_uses', 1)
    
    if count < 1 or count > 100:
        return error_response("批量创建数量应在1-100之间")
    
    if max_uses < 1 or max_uses > 1000:
        return error_response("最大使用次数应在1-1000之间")
    
    try:
        created_codes = []
        admin_id = request.current_user.id
        
        for _ in range(count):
            code = generate_invite_code()
            # 确保邀请码唯一
            while InviteCode.query.filter_by(code=code).first():
                code = generate_invite_code()
            
            invite_code = InviteCode(
                code=code,
                created_by=admin_id,
                note=note,
                max_uses=max_uses
            )
            db.session.add(invite_code)
            created_codes.append(code)
        
        db.session.commit()
        
        log_admin_action(
            'create_invite_codes',
            'invite_code',
            None,
            {'count': count, 'codes': created_codes, 'note': note}
        )
        
        return success_response({
            'codes': created_codes,
            'count': len(created_codes)
        }, f"成功创建{len(created_codes)}个邀请码")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建邀请码失败: {str(e)}")

@bp.route('/invite-codes/<code_id>/toggle', methods=['POST'])
@require_admin()
def toggle_invite_code(code_id):
    """切换邀请码状态"""
    invite_code = InviteCode.query.get_or_404(code_id)
    
    invite_code.is_active = not invite_code.is_active
    db.session.commit()
    
    log_admin_action(
        'toggle_invite_code',
        'invite_code',
        code_id,
        {'is_active': invite_code.is_active}
    )
    
    return success_response({
        'invite_code': invite_code.to_dict()
    }, f"邀请码已{'激活' if invite_code.is_active else '禁用'}")

@bp.route('/rooms', methods=['GET'])
@require_admin()
def list_rooms():
    """获取房间列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '').strip()
    
    query = Room.query
    
    if search:
        query = query.filter(Room.name.contains(search))
    
    query = query.order_by(Room.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'rooms': [room.to_dict() for room in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/rooms/<room_id>/messages', methods=['GET'])
@require_admin()
def get_room_messages(room_id):
    """获取房间聊天记录"""
    room = Room.query.get_or_404(room_id)
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    query = Message.query.filter_by(room_id=room_id).order_by(Message.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'room': room.to_dict(),
        'messages': [msg.to_dict() for msg in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })

@bp.route('/rooms/<room_id>/messages/export', methods=['GET'])
@require_admin()
def export_room_messages(room_id):
    """导出房间聊天记录"""
    room = Room.query.get_or_404(room_id)
    
    messages = Message.query.filter_by(room_id=room_id).order_by(Message.created_at.asc()).all()
    
    # 创建CSV内容
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入标题行
    writer.writerow([
        '时间', '发送者', '用户类型', '消息类型', '内容', '是否撤回', '撤回时间', '回复消息ID'
    ])
    
    # 写入数据行
    for msg in messages:
        writer.writerow([
            msg.created_at.strftime('%Y-%m-%d %H:%M:%S') if msg.created_at else '',
            msg.get_sender_name(),
            '注册用户' if msg.is_user_type() else '匿名用户',
            msg.message_type,
            msg.content if not msg.is_recalled else '[已撤回]',
            '是' if msg.is_recalled else '否',
            msg.recalled_at.strftime('%Y-%m-%d %H:%M:%S') if msg.recalled_at else '',
            msg.reply_to_id or ''
        ])
    
    output.seek(0)
    
    # 创建响应
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename=room_{room_id}_messages.csv'
    
    log_admin_action(
        'export_room_messages',
        'room',
        room_id,
        {'message_count': len(messages)}
    )
    
    return response

@bp.route('/logs', methods=['GET'])
@require_admin()
def get_admin_logs():
    """获取管理员操作日志"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    action = request.args.get('action', '').strip()
    
    query = AdminLog.query
    
    if action:
        query = query.filter(AdminLog.action.contains(action))
    
    query = query.order_by(AdminLog.created_at.desc())
    
    result = paginate_query(query, page, per_page)
    
    return success_response({
        'logs': [log.to_dict() for log in result['items']],
        'pagination': {
            'page': result['page'],
            'per_page': result['per_page'],
            'total': result['total'],
            'pages': result['pages'],
            'has_prev': result['has_prev'],
            'has_next': result['has_next']
        }
    })
