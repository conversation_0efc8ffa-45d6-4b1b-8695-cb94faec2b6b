from . import db, generate_uuid
from datetime import datetime
import json

class Message(db.Model):
    __tablename__ = 'messages'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    room_id = db.Column(db.String(36), db.<PERSON><PERSON>('rooms.id'), nullable=False)
    user_id = db.Column(db.String(36), db.<PERSON>('users.id'), nullable=True)
    anonymous_user_id = db.Column(db.String(36), db.<PERSON>ey('anonymous_users.id'), nullable=True)
    content = db.Column(db.Text, nullable=True)
    message_type = db.Column(db.String(20), default='text')  # text, image, audio, system, questionnaire, profile_reveal
    file_path = db.Column(db.String(255), nullable=True)
    file_name = db.Column(db.String(255), nullable=True)
    file_size = db.Column(db.Integer, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_recalled = db.Column(db.Boolean, default=False)
    recalled_at = db.Column(db.DateTime, nullable=True)
    recalled_by = db.Column(db.String(36), nullable=True)
    reply_to_id = db.Column(db.String(36), db.ForeignKey('messages.id'), nullable=True)
    extra_data = db.Column(db.Text, nullable=True)  # JSON格式存储额外数据
    
    # 关系
    reply_to = db.relationship('Message', remote_side=[id], backref='replies')
    reactions = db.relationship('MessageReaction', backref='message', lazy='dynamic', cascade='all, delete-orphan')
    
    # 确保用户或匿名用户只能有一个
    __table_args__ = (
        db.CheckConstraint('(user_id IS NOT NULL) != (anonymous_user_id IS NOT NULL)', 
                          name='check_message_user_or_anonymous'),
    )
    
    def get_sender_name(self):
        if self.user_id:
            return self.user.username
        elif self.anonymous_user_id:
            return self.anonymous_user.nickname or f'匿名用户{self.anonymous_user.id[:8]}'
        return '系统'
    
    def get_sender_id(self):
        return self.user_id or self.anonymous_user_id
    
    def is_user_type(self):
        return self.user_id is not None
    
    def can_recall(self, user_id=None, is_admin=False):
        if self.is_recalled:
            return False
        if is_admin:
            return True
        if user_id and self.get_sender_id() == user_id:
            # 检查时间限制
            from config.config import Config
            time_limit = datetime.utcnow().timestamp() - self.created_at.timestamp()
            return time_limit <= Config.MESSAGE_RECALL_TIME_LIMIT
        return False
    
    def recall(self, recalled_by=None):
        self.is_recalled = True
        self.recalled_at = datetime.utcnow()
        self.recalled_by = recalled_by
    
    def set_metadata(self, data):
        self.extra_data = json.dumps(data) if data else None

    def get_metadata(self):
        return json.loads(self.extra_data) if self.extra_data else {}
    
    def to_dict(self, include_content=True):
        data = {
            'id': self.id,
            'room_id': self.room_id,
            'sender_id': self.get_sender_id(),
            'sender_name': self.get_sender_name(),
            'is_user_type': self.is_user_type(),
            'message_type': self.message_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_recalled': self.is_recalled,
            'recalled_at': self.recalled_at.isoformat() if self.recalled_at else None,
            'recalled_by': self.recalled_by,
            'reply_to_id': self.reply_to_id,
            'metadata': self.get_metadata()
        }
        
        if include_content and not self.is_recalled:
            data['content'] = self.content
            if self.file_path:
                data['file'] = {
                    'path': self.file_path,
                    'name': self.file_name,
                    'size': self.file_size
                }
        elif self.is_recalled:
            data['content'] = '此消息已被撤回'
            
        # 添加回复信息
        if self.reply_to_id and self.reply_to:
            data['reply_to'] = {
                'id': self.reply_to.id,
                'sender_name': self.reply_to.get_sender_name(),
                'content': self.reply_to.content[:50] + '...' if len(self.reply_to.content or '') > 50 else self.reply_to.content,
                'message_type': self.reply_to.message_type
            }
        
        return data

class MessageReaction(db.Model):
    __tablename__ = 'message_reactions'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    message_id = db.Column(db.String(36), db.ForeignKey('messages.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)
    anonymous_user_id = db.Column(db.String(36), db.ForeignKey('anonymous_users.id'), nullable=True)
    reaction = db.Column(db.String(10), nullable=False)  # emoji
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 确保用户或匿名用户只能有一个，且同一用户对同一消息的同一反应只能有一个
    __table_args__ = (
        db.CheckConstraint('(user_id IS NOT NULL) != (anonymous_user_id IS NOT NULL)', 
                          name='check_reaction_user_or_anonymous'),
        db.UniqueConstraint('message_id', 'user_id', 'reaction', name='unique_user_reaction'),
        db.UniqueConstraint('message_id', 'anonymous_user_id', 'reaction', name='unique_anonymous_reaction')
    )
    
    def get_user_id(self):
        return self.user_id or self.anonymous_user_id
    
    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'user_id': self.get_user_id(),
            'reaction': self.reaction,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
