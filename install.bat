@echo off
chcp 65001 >nul
title 聊天平台 - 安装向导

echo.
echo ========================================
echo         聊天平台安装向导
echo ========================================
echo.
echo 此脚本将帮助您完成聊天平台的初始化设置
echo.

:: 检查Python
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python
    echo.
    echo 请先安装Python 3.8或更高版本:
    echo 📥 官方下载: https://www.python.org/downloads/
    echo 💡 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%
echo.

:: 删除旧的虚拟环境（如果存在）
if exist "venv" (
    echo 🗑️  删除旧的虚拟环境...
    rmdir /s /q venv
)

:: 创建虚拟环境
echo 🔧 创建新的虚拟环境...
python -m venv venv
if errorlevel 1 (
    echo ❌ 虚拟环境创建失败
    pause
    exit /b 1
)
echo ✅ 虚拟环境创建成功

:: 激活虚拟环境
echo 🚀 激活虚拟环境...
call venv\Scripts\activate.bat

:: 升级pip
echo 📦 升级pip到最新版本...
python -m pip install --upgrade pip
echo ✅ pip升级完成

:: 安装依赖
echo 📦 安装项目依赖...
echo 这可能需要几分钟时间，请耐心等待...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    echo 请检查网络连接或联系技术支持
    pause
    exit /b 1
)
echo ✅ 依赖安装成功

:: 创建必要目录
echo 📁 创建项目目录...
if not exist "uploads" mkdir uploads
if not exist "uploads\images" mkdir uploads\images  
if not exist "uploads\audio" mkdir uploads\audio
if not exist "instance" mkdir instance
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

:: 测试安装
echo 🧪 测试安装...
python -c "from app import create_app; print('✅ 安装测试通过')"
if errorlevel 1 (
    echo ❌ 安装测试失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo           🎉 安装完成！
echo ========================================
echo.
echo 📋 安装摘要:
echo   ✅ Python环境: %PYTHON_VERSION%
echo   ✅ 虚拟环境: 已创建并激活
echo   ✅ 项目依赖: 已安装
echo   ✅ 目录结构: 已创建
echo   ✅ 功能测试: 通过
echo.
echo 🚀 下一步:
echo   1. 双击 start_chat_platform.bat 启动完整版
echo   2. 双击 quick_start.bat 快速启动
echo   3. 访问 http://localhost:5000 使用平台
echo.
echo 💡 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 📞 如有问题，请查看README.md或联系技术支持
echo.

pause
