# 聊天平台 - Windows 一键启动说明

## 🚀 快速开始

### 首次使用
1. **双击 `install.bat`** - 完成初始化安装
   - 自动检查Python环境
   - 创建虚拟环境并升级pip
   - 安装所有依赖包
   - 创建必要的目录结构
   - 运行安装测试

### 日常使用
1. **双击 `start_chat_platform.bat`** - 完整启动（推荐）
   - 包含完整的环境检查
   - 自动打开浏览器
   - 显示详细的启动信息

2. **双击 `quick_start.bat`** - 快速启动
   - 适合已经安装完成后的快速启动
   - 启动速度更快

### 停止服务器
- **双击 `stop_server.bat`** - 安全停止服务器
- 或在服务器窗口按 `Ctrl+C`

## 📋 文件说明

| 文件名 | 用途 | 使用场景 |
|--------|------|----------|
| `install.bat` | 初始化安装 | 首次使用或重新安装 |
| `start_chat_platform.bat` | 完整启动 | 日常使用（推荐） |
| `quick_start.bat` | 快速启动 | 已安装后的快速启动 |
| `stop_server.bat` | 停止服务器 | 安全关闭服务器 |

## 🔧 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8 或更高版本
- **内存**: 至少 2GB 可用内存
- **磁盘**: 至少 500MB 可用空间

## 📍 访问地址

启动成功后，可通过以下地址访问：

- **主页**: http://localhost:5000
- **API测试页面**: http://localhost:5000
- **API信息**: http://localhost:5000/api

## 👤 默认账户

- **管理员用户名**: admin
- **管理员密码**: admin123

## ❓ 常见问题

### Q: 提示"未检测到Python"
**A**: 请先安装Python 3.8+，下载地址：https://www.python.org/downloads/
安装时务必勾选"Add Python to PATH"选项。

### Q: 依赖安装失败
**A**: 
1. 检查网络连接
2. 尝试使用国内镜像：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`
3. 重新运行 `install.bat`

### Q: 服务器启动失败
**A**:
1. 检查端口5000是否被占用
2. 查看错误信息
3. 重新运行 `install.bat` 重新安装

### Q: 浏览器无法访问
**A**:
1. 确认服务器已启动（看到"Running on http://127.0.0.1:5000"）
2. 检查防火墙设置
3. 尝试访问 http://127.0.0.1:5000

## 🛠️ 高级使用

### 修改端口
编辑 `run.py` 文件，修改 `port=5000` 为其他端口。

### 生产环境部署
请参考主 `README.md` 文件中的部署说明。

### 开发模式
如需进行开发，请：
1. 激活虚拟环境：`venv\Scripts\activate`
2. 手动启动：`python run.py`

## 📞 技术支持

如遇到问题，请：
1. 查看控制台错误信息
2. 检查 `logs` 目录下的日志文件
3. 参考主 `README.md` 文件
4. 联系技术支持

---

**祝您使用愉快！** 🎉
