<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天平台 - 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .api-list {
            list-style: none;
            padding: 0;
        }
        .api-list li {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .method {
            font-weight: bold;
            color: #007bff;
            margin-right: 10px;
        }
        .endpoint {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.ready {
            background: #d4edda;
            color: #155724;
        }
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 聊天平台后端 API 测试</h1>
        
        <div class="section">
            <h2>📊 系统状态</h2>
            <p><strong>服务器状态:</strong> <span class="status ready">运行中</span></p>
            <p><strong>数据库:</strong> SQLite</p>
            <p><strong>WebSocket:</strong> Socket.IO</p>
            <p><strong>默认管理员:</strong> admin / admin123</p>
        </div>

        <div class="section">
            <h2>🔐 认证相关 API</h2>
            <ul class="api-list">
                <li><span class="method">POST</span> <span class="endpoint">/api/auth/register</span> - 用户注册</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/auth/login</span> - 用户登录</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/auth/anonymous</span> - 创建匿名用户</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/auth/switch-to-user</span> - 匿名用户转注册用户</li>
            </ul>
            
            <div class="test-section">
                <h3>快速测试</h3>
                <button onclick="testCreateAnonymous()">创建匿名用户</button>
                <button onclick="testLogin()">管理员登录</button>
                <div id="auth-result" class="result" style="display:none;"></div>
            </div>
        </div>

        <div class="section">
            <h2>🏠 房间管理 API</h2>
            <ul class="api-list">
                <li><span class="method">GET</span> <span class="endpoint">/api/rooms/</span> - 获取房间列表</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/rooms/</span> - 创建房间</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/rooms/{id}/join</span> - 加入房间</li>
                <li><span class="method">GET</span> <span class="endpoint">/api/rooms/{id}/members</span> - 获取房间成员</li>
            </ul>
        </div>

        <div class="section">
            <h2>💬 消息相关 API</h2>
            <ul class="api-list">
                <li><span class="method">GET</span> <span class="endpoint">/api/messages/{room_id}</span> - 获取消息历史</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/messages/{room_id}</span> - 发送消息</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/messages/{id}/recall</span> - 撤回消息</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/messages/{id}/react</span> - 消息反应</li>
            </ul>
        </div>

        <div class="section">
            <h2>📁 文件上传 API</h2>
            <ul class="api-list">
                <li><span class="method">POST</span> <span class="endpoint">/api/files/upload/image/{room_id}</span> - 上传图片</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/files/upload/audio/{room_id}</span> - 上传语音</li>
                <li><span class="method">GET</span> <span class="endpoint">/api/files/download/{path}</span> - 下载文件</li>
            </ul>
        </div>

        <div class="section">
            <h2>👤 人物信息 API</h2>
            <ul class="api-list">
                <li><span class="method">GET</span> <span class="endpoint">/api/profiles/categories</span> - 获取信息分类</li>
                <li><span class="method">GET</span> <span class="endpoint">/api/profiles/my-profile</span> - 获取我的信息</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/profiles/my-profile</span> - 更新我的信息</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/profiles/reveal/{room_id}</span> - 揭露信息</li>
            </ul>
        </div>

        <div class="section">
            <h2>📋 问卷系统 API</h2>
            <ul class="api-list">
                <li><span class="method">GET</span> <span class="endpoint">/api/questionnaires/</span> - 获取问卷列表</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/questionnaires/</span> - 创建问卷</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/questionnaires/{id}/send/{room_id}</span> - 发送问卷</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/questionnaires/respond/{instance_id}</span> - 回答问卷</li>
            </ul>
        </div>

        <div class="section">
            <h2>⚙️ 管理员 API</h2>
            <ul class="api-list">
                <li><span class="method">GET</span> <span class="endpoint">/api/admin/dashboard</span> - 管理员仪表板</li>
                <li><span class="method">GET</span> <span class="endpoint">/api/admin/users</span> - 用户管理</li>
                <li><span class="method">POST</span> <span class="endpoint">/api/admin/invite-codes</span> - 创建邀请码</li>
                <li><span class="method">GET</span> <span class="endpoint">/api/admin/logs</span> - 操作日志</li>
            </ul>
        </div>

        <div class="section">
            <h2>🔌 WebSocket 事件</h2>
            <ul class="api-list">
                <li><strong>connect</strong> - 连接到服务器</li>
                <li><strong>join_room</strong> - 加入房间</li>
                <li><strong>leave_room</strong> - 离开房间</li>
                <li><strong>send_message</strong> - 发送消息</li>
                <li><strong>typing</strong> - 正在输入状态</li>
            </ul>
        </div>
    </div>

    <script>
        async function testCreateAnonymous() {
            const result = document.getElementById('auth-result');
            result.style.display = 'block';
            result.textContent = '正在创建匿名用户...';
            
            try {
                const response = await fetch('/api/auth/anonymous', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        nickname: '测试用户' + Math.floor(Math.random() * 1000)
                    })
                });
                
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }

        async function testLogin() {
            const result = document.getElementById('auth-result');
            result.style.display = 'block';
            result.textContent = '正在登录管理员账户...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
