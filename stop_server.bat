@echo off
chcp 65001 >nul
title 停止聊天平台服务器

echo 🛑 正在停止聊天平台服务器...

:: 查找并终止Python进程（运行run.py的进程）
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh ^| findstr run.py') do (
    echo 🔍 找到服务器进程 PID: %%i
    taskkill /pid %%i /f
    echo ✅ 服务器已停止
    goto :done
)

:: 如果没有找到特定进程，尝试停止所有Python进程
tasklist /fi "imagename eq python.exe" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  未找到特定服务器进程，是否要停止所有Python进程？
    echo 按任意键继续，或关闭窗口取消...
    pause >nul
    taskkill /im python.exe /f
    echo ✅ 所有Python进程已停止
) else (
    echo ℹ️  未找到运行中的Python进程
)

:done
echo.
echo 👋 操作完成
pause
