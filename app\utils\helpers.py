from flask import request, jsonify, current_app
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from functools import wraps
from app.models import db
from app.models.user import User, AnonymousUser
from app.models.admin import AdminLog
import uuid
import secrets
import string
import os
from PIL import Image
import hashlib

def success_response(data=None, message="Success", code=200):
    """统一成功响应格式"""
    response = {
        'success': True,
        'message': message,
        'code': code
    }
    if data is not None:
        response['data'] = data
    return jsonify(response), code

def error_response(message="Error", code=400, details=None):
    """统一错误响应格式"""
    response = {
        'success': False,
        'message': message,
        'code': code
    }
    if details:
        response['details'] = details
    return jsonify(response), code

def get_current_user():
    """获取当前用户（注册用户或匿名用户）"""
    try:
        # 尝试获取JWT用户
        verify_jwt_in_request(optional=True)
        user_id = get_jwt_identity()
        if user_id:
            user = User.query.get(user_id)
            if user and user.is_active:
                return user, 'user'
    except:
        pass
    
    # 尝试获取匿名用户
    session_id = request.headers.get('X-Session-ID')
    if session_id:
        anonymous_user = AnonymousUser.query.filter_by(session_id=session_id).first()
        if anonymous_user:
            return anonymous_user, 'anonymous'
    
    return None, None

def require_auth(allow_anonymous=True):
    """认证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user, user_type = get_current_user()
            if not user:
                if allow_anonymous:
                    # 创建新的匿名用户
                    session_id = request.headers.get('X-Session-ID')
                    if not session_id:
                        return error_response("Missing session ID", 401)
                    
                    anonymous_user = AnonymousUser(session_id=session_id)
                    db.session.add(anonymous_user)
                    db.session.commit()
                    user, user_type = anonymous_user, 'anonymous'
                else:
                    return error_response("Authentication required", 401)
            
            # 将用户信息添加到请求上下文
            request.current_user = user
            request.user_type = user_type
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_admin():
    """管理员权限装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user, user_type = get_current_user()
            if not user or user_type != 'user' or not user.is_admin:
                return error_response("Admin access required", 403)
            
            request.current_user = user
            request.user_type = user_type
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_admin_action(action, target_type=None, target_id=None, details=None):
    """记录管理员操作"""
    if hasattr(request, 'current_user') and request.current_user.is_admin:
        AdminLog.log_action(
            admin_id=request.current_user.id,
            action=action,
            target_type=target_type,
            target_id=target_id,
            details=details,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

def generate_invite_code(length=8):
    """生成邀请码"""
    characters = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(characters) for _ in range(length))

def generate_session_id():
    """生成会话ID"""
    return str(uuid.uuid4())

def allowed_file(filename, allowed_extensions):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def secure_filename(filename):
    """生成安全的文件名"""
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    name = hashlib.md5(f"{filename}{uuid.uuid4()}".encode()).hexdigest()
    return f"{name}.{ext}" if ext else name

def compress_image(image_path, max_size=(1920, 1080), quality=85):
    """压缩图片"""
    try:
        with Image.open(image_path) as img:
            # 转换为RGB模式（如果是RGBA）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 计算新尺寸
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 保存压缩后的图片
            img.save(image_path, 'JPEG', quality=quality, optimize=True)
            return True
    except Exception as e:
        current_app.logger.error(f"Image compression failed: {e}")
        return False

def get_file_size(file_path):
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except:
        return 0

def validate_request_data(data, required_fields):
    """验证请求数据"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    
    if missing_fields:
        return False, f"Missing required fields: {', '.join(missing_fields)}"
    
    return True, None

def paginate_query(query, page=1, per_page=10, max_per_page=100):
    """分页查询"""
    if per_page > max_per_page:
        per_page = max_per_page
    
    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    
    return {
        'items': items,
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': (total + per_page - 1) // per_page,
        'has_prev': page > 1,
        'has_next': page * per_page < total
    }
