// 文件管理

class FileManager {
    constructor() {
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        this.allowedAudioTypes = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];
        this.allowedFileTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/zip',
            'application/x-rar-compressed'
        ];
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听拖拽事件
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.showDropZone();
        });
        
        document.addEventListener('dragleave', (e) => {
            if (!e.relatedTarget) {
                this.hideDropZone();
            }
        });
        
        document.addEventListener('drop', (e) => {
            e.preventDefault();
            this.hideDropZone();
            
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0 && AppState.currentRoom) {
                this.handleFileUpload(files);
            }
        });
    }
    
    // 显示文件上传对话框
    showFileUploadDialog() {
        if (!AppState.currentRoom) {
            showNotification('请先加入一个房间', 'error');
            return;
        }
        
        const content = `
            <div class="file-upload-dialog">
                <div class="upload-tabs">
                    <button class="tab-btn active" data-tab="image">图片</button>
                    <button class="tab-btn" data-tab="audio">语音</button>
                    <button class="tab-btn" data-tab="file">文件</button>
                </div>
                
                <!-- 图片上传 -->
                <div id="image-upload" class="upload-tab active">
                    <div class="upload-area" onclick="document.getElementById('image-input').click()">
                        <div class="upload-icon">🖼️</div>
                        <div class="upload-text">点击选择图片或拖拽到此处</div>
                        <div class="upload-hint">支持 JPG, PNG, GIF, WebP 格式，最大 10MB</div>
                    </div>
                    <input type="file" id="image-input" accept="image/*" multiple style="display: none;">
                    <div id="image-preview" class="file-preview"></div>
                </div>
                
                <!-- 语音上传 -->
                <div id="audio-upload" class="upload-tab">
                    <div class="upload-area" onclick="document.getElementById('audio-input').click()">
                        <div class="upload-icon">🎵</div>
                        <div class="upload-text">点击选择音频文件</div>
                        <div class="upload-hint">支持 MP3, WAV, OGG, M4A 格式，最大 10MB</div>
                    </div>
                    <input type="file" id="audio-input" accept="audio/*" style="display: none;">
                    <div class="audio-recorder">
                        <button id="record-btn" class="btn btn-secondary">🎤 开始录音</button>
                        <div id="recording-status" class="recording-status" style="display: none;">
                            <span class="recording-dot"></span>
                            <span id="recording-time">00:00</span>
                            <button id="stop-record-btn" class="btn btn-danger btn-sm">停止</button>
                        </div>
                    </div>
                    <div id="audio-preview" class="file-preview"></div>
                </div>
                
                <!-- 文件上传 -->
                <div id="file-upload" class="upload-tab">
                    <div class="upload-area" onclick="document.getElementById('file-input').click()">
                        <div class="upload-icon">📎</div>
                        <div class="upload-text">点击选择文件</div>
                        <div class="upload-hint">支持常见文档格式，最大 10MB</div>
                    </div>
                    <input type="file" id="file-input" multiple style="display: none;">
                    <div id="file-preview" class="file-preview"></div>
                </div>
                
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="window.fileManager.uploadSelectedFiles()">发送</button>
                </div>
            </div>
        `;
        
        window.app.showModal(content, '文件上传');
        
        // 设置事件监听器
        setTimeout(() => {
            this.setupUploadDialogEvents();
        }, 100);
    }
    
    // 设置上传对话框事件
    setupUploadDialogEvents() {
        // 标签页切换
        DOM.$$('.upload-tabs .tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.switchUploadTab(tab);
            });
        });
        
        // 文件选择
        const imageInput = DOM.$('#image-input');
        const audioInput = DOM.$('#audio-input');
        const fileInput = DOM.$('#file-input');
        
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files, 'image');
            });
        }
        
        if (audioInput) {
            audioInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files, 'audio');
            });
        }
        
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files, 'file');
            });
        }
        
        // 录音功能
        this.setupAudioRecorder();
    }
    
    // 切换上传标签页
    switchUploadTab(tab) {
        // 更新标签按钮
        DOM.$$('.upload-tabs .tab-btn').forEach(btn => {
            DOM.removeClass(btn, 'active');
        });
        DOM.addClass(DOM.$(`[data-tab="${tab}"]`), 'active');
        
        // 更新内容
        DOM.$$('.upload-tab').forEach(content => {
            DOM.removeClass(content, 'active');
        });
        DOM.addClass(DOM.$(`#${tab}-upload`), 'active');
    }
    
    // 处理文件选择
    handleFileSelection(files, type) {
        const fileArray = Array.from(files);
        const previewContainer = DOM.$(`#${type}-preview`);
        
        previewContainer.innerHTML = '';
        
        fileArray.forEach(file => {
            if (this.validateFile(file, type)) {
                const previewItem = this.createFilePreview(file, type);
                previewContainer.appendChild(previewItem);
            }
        });
    }
    
    // 验证文件
    validateFile(file, type) {
        // 检查文件大小
        if (file.size > this.maxFileSize) {
            showNotification(`文件 ${file.name} 超过最大限制 10MB`, 'error');
            return false;
        }
        
        // 检查文件类型
        let allowedTypes = [];
        switch (type) {
            case 'image':
                allowedTypes = this.allowedImageTypes;
                break;
            case 'audio':
                allowedTypes = this.allowedAudioTypes;
                break;
            case 'file':
                allowedTypes = this.allowedFileTypes;
                break;
        }
        
        if (!allowedTypes.includes(file.type)) {
            showNotification(`不支持的文件类型: ${file.name}`, 'error');
            return false;
        }
        
        return true;
    }
    
    // 创建文件预览
    createFilePreview(file, type) {
        const previewItem = DOM.create('div', {
            className: 'file-preview-item',
            dataset: { fileName: file.name, fileType: type }
        });
        
        let previewContent = '';
        
        switch (type) {
            case 'image':
                const imageUrl = URL.createObjectURL(file);
                previewContent = `
                    <div class="preview-image">
                        <img src="${imageUrl}" alt="${file.name}">
                    </div>
                    <div class="preview-info">
                        <div class="file-name">${StringUtils.truncate(file.name, 20)}</div>
                        <div class="file-size">${FileUtils.formatSize(file.size)}</div>
                    </div>
                `;
                break;
                
            case 'audio':
                const audioUrl = URL.createObjectURL(file);
                previewContent = `
                    <div class="preview-audio">
                        <audio controls>
                            <source src="${audioUrl}" type="${file.type}">
                        </audio>
                    </div>
                    <div class="preview-info">
                        <div class="file-name">${StringUtils.truncate(file.name, 20)}</div>
                        <div class="file-size">${FileUtils.formatSize(file.size)}</div>
                    </div>
                `;
                break;
                
            case 'file':
                const icon = FileUtils.getFileIcon(file.name);
                previewContent = `
                    <div class="preview-file">
                        <div class="file-icon">${icon}</div>
                    </div>
                    <div class="preview-info">
                        <div class="file-name">${StringUtils.truncate(file.name, 20)}</div>
                        <div class="file-size">${FileUtils.formatSize(file.size)}</div>
                    </div>
                `;
                break;
        }
        
        previewItem.innerHTML = `
            ${previewContent}
            <button class="preview-remove" onclick="this.parentElement.remove()">×</button>
        `;
        
        // 存储文件对象
        previewItem._file = file;
        
        return previewItem;
    }
    
    // 设置音频录音器
    setupAudioRecorder() {
        const recordBtn = DOM.$('#record-btn');
        const stopBtn = DOM.$('#stop-record-btn');
        const recordingStatus = DOM.$('#recording-status');
        const recordingTime = DOM.$('#recording-time');
        
        let mediaRecorder = null;
        let recordingTimer = null;
        let recordingStartTime = 0;
        
        if (recordBtn) {
            recordBtn.addEventListener('click', async () => {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    
                    const chunks = [];
                    mediaRecorder.ondataavailable = (e) => {
                        chunks.push(e.data);
                    };
                    
                    mediaRecorder.onstop = () => {
                        const blob = new Blob(chunks, { type: 'audio/wav' });
                        const file = new File([blob], `录音_${Date.now()}.wav`, { type: 'audio/wav' });
                        
                        // 添加到预览
                        const previewContainer = DOM.$('#audio-preview');
                        const previewItem = this.createFilePreview(file, 'audio');
                        previewContainer.appendChild(previewItem);
                        
                        // 停止所有音轨
                        stream.getTracks().forEach(track => track.stop());
                    };
                    
                    mediaRecorder.start();
                    recordingStartTime = Date.now();
                    
                    // 更新UI
                    DOM.hide(recordBtn);
                    DOM.show(recordingStatus);
                    
                    // 开始计时
                    recordingTimer = setInterval(() => {
                        const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
                        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
                        const seconds = (elapsed % 60).toString().padStart(2, '0');
                        recordingTime.textContent = `${minutes}:${seconds}`;
                    }, 1000);
                    
                } catch (error) {
                    console.error('录音失败:', error);
                    showNotification('录音功能需要麦克风权限', 'error');
                }
            });
        }
        
        if (stopBtn) {
            stopBtn.addEventListener('click', () => {
                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    mediaRecorder.stop();
                    
                    // 清理计时器
                    if (recordingTimer) {
                        clearInterval(recordingTimer);
                        recordingTimer = null;
                    }
                    
                    // 重置UI
                    DOM.show(recordBtn);
                    DOM.hide(recordingStatus);
                    recordingTime.textContent = '00:00';
                }
            });
        }
    }
    
    // 上传选中的文件
    async uploadSelectedFiles() {
        const previewItems = DOM.$$('.file-preview-item');
        
        if (previewItems.length === 0) {
            showNotification('请选择要上传的文件', 'error');
            return;
        }
        
        const files = Array.from(previewItems).map(item => item._file).filter(Boolean);
        
        if (files.length === 0) {
            showNotification('没有有效的文件', 'error');
            return;
        }
        
        window.app.closeModal();
        
        // 逐个上传文件
        for (const file of files) {
            await this.uploadSingleFile(file);
        }
    }
    
    // 上传单个文件
    async uploadSingleFile(file) {
        try {
            showNotification(`正在上传 ${file.name}...`, 'info', 2000);
            
            let response;
            const fileType = this.getFileType(file);
            
            if (fileType === 'image') {
                response = await FilesAPI.uploadImage(file);
            } else if (fileType === 'audio') {
                response = await FilesAPI.uploadAudio(file);
            } else {
                // 其他文件类型暂时不支持
                showNotification(`暂不支持上传 ${file.name}`, 'warning');
                return;
            }
            
            showNotification(`${file.name} 上传成功`, 'success');
            
        } catch (error) {
            console.error('文件上传失败:', error);
            showNotification(`${file.name} 上传失败`, 'error');
        }
    }
    
    // 获取文件类型
    getFileType(file) {
        if (this.allowedImageTypes.includes(file.type)) {
            return 'image';
        } else if (this.allowedAudioTypes.includes(file.type)) {
            return 'audio';
        } else {
            return 'file';
        }
    }
    
    // 处理文件上传（拖拽）
    async handleFileUpload(files) {
        if (!AppState.currentRoom) {
            showNotification('请先加入一个房间', 'error');
            return;
        }
        
        const validFiles = files.filter(file => {
            const type = this.getFileType(file);
            return this.validateFile(file, type);
        });
        
        if (validFiles.length === 0) {
            showNotification('没有有效的文件', 'error');
            return;
        }
        
        // 直接上传文件
        for (const file of validFiles) {
            await this.uploadSingleFile(file);
        }
    }
    
    // 显示拖拽区域
    showDropZone() {
        let dropZone = DOM.$('#drop-zone');
        if (!dropZone) {
            dropZone = DOM.create('div', {
                id: 'drop-zone',
                className: 'drop-zone',
                innerHTML: `
                    <div class="drop-zone-content">
                        <div class="drop-zone-icon">📎</div>
                        <div class="drop-zone-text">拖拽文件到此处上传</div>
                    </div>
                `
            });
            document.body.appendChild(dropZone);
        }
        
        DOM.show(dropZone);
    }
    
    // 隐藏拖拽区域
    hideDropZone() {
        const dropZone = DOM.$('#drop-zone');
        if (dropZone) {
            DOM.hide(dropZone);
        }
    }
    
    // 下载文件
    downloadFile(url, filename) {
        const link = DOM.create('a', {
            href: url,
            download: filename,
            style: 'display: none'
        });
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // 显示图片预览
    showImagePreview(imageUrl) {
        const content = `
            <div class="image-preview-modal">
                <img src="${imageUrl}" alt="图片预览" style="max-width: 100%; max-height: 80vh;">
            </div>
        `;
        
        window.app.showModal(content, '图片预览');
    }
    
    // 播放音频
    playAudio(audioUrl, button) {
        // 停止其他正在播放的音频
        const otherAudios = DOM.$$('audio');
        otherAudios.forEach(audio => {
            if (!audio.paused) {
                audio.pause();
                audio.currentTime = 0;
            }
        });
        
        // 重置其他播放按钮
        const otherButtons = DOM.$$('.audio-play-btn');
        otherButtons.forEach(btn => {
            btn.textContent = '▶️';
        });
        
        // 创建或获取音频元素
        let audio = button.nextElementSibling;
        if (!audio || audio.tagName !== 'AUDIO') {
            audio = DOM.create('audio', {
                src: audioUrl,
                style: 'display: none'
            });
            button.parentNode.insertBefore(audio, button.nextSibling);
        }
        
        if (audio.paused) {
            audio.play();
            button.textContent = '⏸️';
            
            audio.onended = () => {
                button.textContent = '▶️';
            };
        } else {
            audio.pause();
            button.textContent = '▶️';
        }
    }
}

// 初始化文件管理器
document.addEventListener('DOMContentLoaded', () => {
    window.fileManager = new FileManager();
});
