from . import db, generate_uuid
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

class Room(db.Model):
    __tablename__ = 'rooms'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    creator_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_public = db.Column(db.<PERSON><PERSON>an, default=False)
    password_hash = db.Column(db.String(255), nullable=True)
    is_active = db.Column(db.Bo<PERSON>, default=True)
    max_members = db.Column(db.Integer, default=100)
    
    # 关系
    members = db.relationship('RoomMember', backref='room', lazy='dynamic', cascade='all, delete-orphan')
    messages = db.relationship('Message', backref='room', lazy='dynamic', cascade='all, delete-orphan')
    invite_links = db.relationship('RoomInviteLink', backref='room', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        if password:
            self.password_hash = generate_password_hash(password)
        else:
            self.password_hash = None
    
    def check_password(self, password):
        if not self.password_hash:
            return True
        return check_password_hash(self.password_hash, password)
    
    def has_password(self):
        return self.password_hash is not None
    
    def get_member_count(self):
        return self.members.filter_by(is_active=True).count()
    
    def get_online_count(self):
        return self.members.filter_by(is_active=True, is_online=True).count()
    
    def can_join(self):
        return self.is_active and self.get_member_count() < self.max_members
    
    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'creator_id': self.creator_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_public': self.is_public,
            'has_password': self.has_password(),
            'is_active': self.is_active,
            'max_members': self.max_members,
            'member_count': self.get_member_count(),
            'online_count': self.get_online_count()
        }
        return data

class RoomMember(db.Model):
    __tablename__ = 'room_members'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    room_id = db.Column(db.String(36), db.ForeignKey('rooms.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)
    anonymous_user_id = db.Column(db.String(36), db.ForeignKey('anonymous_users.id'), nullable=True)
    joined_at = db.Column(db.DateTime, default=datetime.utcnow)
    left_at = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    is_online = db.Column(db.Boolean, default=False)
    last_seen = db.Column(db.DateTime, default=datetime.utcnow)
    role = db.Column(db.String(20), default='member')  # member, moderator, admin
    
    # 确保用户或匿名用户只能有一个
    __table_args__ = (
        db.CheckConstraint('(user_id IS NOT NULL) != (anonymous_user_id IS NOT NULL)', 
                          name='check_user_or_anonymous'),
        db.UniqueConstraint('room_id', 'user_id', name='unique_room_user'),
        db.UniqueConstraint('room_id', 'anonymous_user_id', name='unique_room_anonymous')
    )
    
    def get_display_name(self):
        if self.user_id:
            return self.user.username
        elif self.anonymous_user_id:
            return self.anonymous_user.nickname or f'匿名用户{self.anonymous_user.id[:8]}'
        return '未知用户'
    
    def get_user_id(self):
        return self.user_id or self.anonymous_user_id
    
    def is_user_type(self):
        return self.user_id is not None
    
    def to_dict(self):
        return {
            'id': self.id,
            'room_id': self.room_id,
            'user_id': self.get_user_id(),
            'display_name': self.get_display_name(),
            'is_user_type': self.is_user_type(),
            'joined_at': self.joined_at.isoformat() if self.joined_at else None,
            'left_at': self.left_at.isoformat() if self.left_at else None,
            'is_active': self.is_active,
            'is_online': self.is_online,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None,
            'role': self.role
        }

class RoomInviteLink(db.Model):
    __tablename__ = 'room_invite_links'
    
    id = db.Column(db.String(36), primary_key=True, default=generate_uuid)
    room_id = db.Column(db.String(36), db.ForeignKey('rooms.id'), nullable=False)
    token = db.Column(db.String(64), unique=True, nullable=False, index=True)
    created_by = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)
    max_uses = db.Column(db.Integer, default=10)
    current_uses = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    
    # 关系
    creator = db.relationship('User', backref='created_invite_links')
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.token:
            self.token = secrets.token_urlsafe(32)
        if not self.expires_at:
            self.expires_at = datetime.utcnow() + timedelta(hours=24)
    
    def is_valid(self):
        return (self.is_active and 
                self.expires_at > datetime.utcnow() and 
                self.current_uses < self.max_uses)
    
    def use_link(self):
        if not self.is_valid():
            return False
        self.current_uses += 1
        return True
    
    def get_url(self, base_url):
        return f"{base_url}/join/{self.token}"
    
    def to_dict(self, base_url=None):
        data = {
            'id': self.id,
            'room_id': self.room_id,
            'token': self.token,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'max_uses': self.max_uses,
            'current_uses': self.current_uses,
            'is_active': self.is_active,
            'is_valid': self.is_valid()
        }
        if base_url:
            data['url'] = self.get_url(base_url)
        return data
