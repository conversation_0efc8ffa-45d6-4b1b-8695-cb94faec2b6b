from flask import Blueprint, request, jsonify
from app.models import db
from app.models.user import User, AnonymousUser, InviteCode
from app.utils.helpers import success_response, error_response, validate_request_data, generate_session_id
from datetime import datetime
import uuid

bp = Blueprint('auth', __name__)

@bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()
    
    # 验证必填字段
    valid, error_msg = validate_request_data(data, ['username', 'password', 'confirm_password', 'invite_code'])
    if not valid:
        return error_response(error_msg)
    
    username = data['username'].strip()
    password = data['password']
    confirm_password = data['confirm_password']
    invite_code = data['invite_code'].strip().upper()
    
    # 验证密码
    if password != confirm_password:
        return error_response("密码确认不匹配")
    
    if len(password) < 6:
        return error_response("密码长度至少6位")
    
    # 验证用户名
    if len(username) < 3 or len(username) > 20:
        return error_response("用户名长度应在3-20位之间")
    
    if User.query.filter_by(username=username).first():
        return error_response("用户名已存在")
    
    # 验证邀请码
    invite = InviteCode.query.filter_by(code=invite_code, is_active=True).first()
    if not invite or not invite.is_valid():
        return error_response("邀请码无效或已过期")
    
    try:
        # 创建用户
        user = User(username=username)
        user.set_password(password)
        db.session.add(user)
        db.session.flush()  # 获取用户ID
        
        # 使用邀请码
        invite.use_code(user.id)
        
        db.session.commit()
        
        return success_response({
            'user': user.to_dict(),
            'token': user.generate_token()
        }, "注册成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"注册失败: {str(e)}")

@bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['username', 'password'])
    if not valid:
        return error_response(error_msg)
    
    username = data['username'].strip()
    password = data['password']
    
    user = User.query.filter_by(username=username).first()
    
    if not user or not user.check_password(password):
        return error_response("用户名或密码错误")
    
    if not user.is_active:
        return error_response("账户已被禁用")
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    db.session.commit()
    
    return success_response({
        'user': user.to_dict(),
        'token': user.generate_token()
    }, "登录成功")

@bp.route('/anonymous', methods=['POST'])
def create_anonymous():
    """创建匿名用户"""
    data = request.get_json() or {}
    
    session_id = data.get('session_id')
    if not session_id:
        session_id = generate_session_id()
    
    # 检查是否已存在
    existing = AnonymousUser.query.filter_by(session_id=session_id).first()
    if existing:
        existing.last_active = datetime.utcnow()
        db.session.commit()
        return success_response({
            'user': existing.to_dict(),
            'session_id': session_id
        }, "匿名用户已存在")
    
    try:
        anonymous_user = AnonymousUser(
            session_id=session_id,
            nickname=data.get('nickname')
        )
        db.session.add(anonymous_user)
        db.session.commit()
        
        return success_response({
            'user': anonymous_user.to_dict(),
            'session_id': session_id
        }, "匿名用户创建成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"创建匿名用户失败: {str(e)}")

@bp.route('/switch-anonymous', methods=['POST'])
def switch_anonymous():
    """已登录用户切换到匿名模式"""
    data = request.get_json() or {}
    
    session_id = data.get('session_id')
    if not session_id:
        session_id = generate_session_id()
    
    # 检查是否已存在该session_id的匿名用户
    existing = AnonymousUser.query.filter_by(session_id=session_id).first()
    if existing:
        existing.last_active = datetime.utcnow()
        db.session.commit()
        return success_response({
            'user': existing.to_dict(),
            'session_id': session_id
        }, "切换到匿名模式")
    
    try:
        anonymous_user = AnonymousUser(
            session_id=session_id,
            nickname=data.get('nickname')
        )
        db.session.add(anonymous_user)
        db.session.commit()
        
        return success_response({
            'user': anonymous_user.to_dict(),
            'session_id': session_id
        }, "切换到匿名模式")
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"切换匿名模式失败: {str(e)}")

@bp.route('/validate-invite', methods=['POST'])
def validate_invite():
    """验证邀请码"""
    data = request.get_json()
    
    valid, error_msg = validate_request_data(data, ['invite_code'])
    if not valid:
        return error_response(error_msg)
    
    invite_code = data['invite_code'].strip().upper()
    
    invite = InviteCode.query.filter_by(code=invite_code, is_active=True).first()
    if not invite or not invite.is_valid():
        return error_response("邀请码无效或已过期")
    
    return success_response({
        'valid': True,
        'remaining_uses': invite.max_uses - invite.current_uses
    }, "邀请码有效")

@bp.route('/user-info', methods=['GET'])
def get_user_info():
    """获取当前用户信息"""
    from app.utils.helpers import get_current_user
    
    user, user_type = get_current_user()
    if not user:
        return error_response("未登录", 401)
    
    return success_response({
        'user': user.to_dict(),
        'user_type': user_type
    })
