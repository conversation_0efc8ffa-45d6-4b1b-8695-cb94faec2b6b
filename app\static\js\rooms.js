// 房间管理

class RoomsManager {
    constructor() {
        this.rooms = [];
        this.currentRoom = null;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听侧边栏视图切换
        EventBus.on('sidebar:viewChanged', (view) => {
            if (view === 'rooms') {
                this.loadRooms();
            }
        });
        
        // 监听房间更新事件
        EventBus.on('room:updated', (data) => {
            this.handleRoomUpdate(data);
        });
        
        // 监听用户加入/离开事件
        EventBus.on('user:joined', (data) => {
            this.handleUserJoined(data);
        });
        
        EventBus.on('user:left', (data) => {
            this.handleUserLeft(data);
        });
    }
    
    // 加载房间列表
    async loadRooms() {
        try {
            const response = await RoomsAPI.getRooms();
            this.rooms = response.rooms || [];
            AppState.rooms = this.rooms;
            
            this.renderRoomsList();
            
        } catch (error) {
            console.error('加载房间列表失败:', error);
            showNotification('加载房间列表失败', 'error');
        }
    }
    
    // 渲染房间列表
    renderRoomsList() {
        const roomsList = DOM.$('#rooms-list');
        if (!roomsList) return;
        
        if (this.rooms.length === 0) {
            roomsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🏠</div>
                    <div class="empty-state-title">暂无房间</div>
                    <div class="empty-state-description">创建一个房间开始聊天吧</div>
                </div>
            `;
            return;
        }
        
        const roomsHTML = this.rooms.map(room => this.createRoomItemHTML(room)).join('');
        roomsList.innerHTML = roomsHTML;
        
        // 添加点击事件监听器
        roomsList.querySelectorAll('.room-item').forEach(item => {
            item.addEventListener('click', () => {
                const roomId = item.dataset.roomId;
                this.joinRoom(roomId);
            });
        });
    }
    
    // 创建房间项HTML
    createRoomItemHTML(room) {
        const isActive = this.currentRoom && this.currentRoom.id === room.id;
        const lastActivity = room.last_activity ? 
            TimeUtils.formatTime(room.last_activity) : '无活动';
        
        return `
            <div class="room-item ${isActive ? 'active' : ''}" data-room-id="${room.id}">
                <div class="room-name">${StringUtils.escapeHtml(room.name)}</div>
                <div class="room-description">${StringUtils.escapeHtml(room.description || '')}</div>
                <div class="room-meta">
                    <span class="room-members-count">${room.member_count || 0}</span>
                    <span class="room-last-activity">${lastActivity}</span>
                </div>
            </div>
        `;
    }
    
    // 加入房间
    async joinRoom(roomId, password = null) {
        try {
            // 如果已经在这个房间，直接切换
            if (this.currentRoom && this.currentRoom.id === roomId) {
                this.switchToRoom(this.currentRoom);
                return;
            }
            
            // 离开当前房间
            if (this.currentRoom) {
                await this.leaveCurrentRoom();
            }
            
            // 加入新房间
            const response = await RoomsAPI.joinRoom(roomId, password);
            
            // 获取房间详细信息
            const roomInfo = await RoomsAPI.getRoomInfo(roomId);
            
            this.currentRoom = roomInfo.room;
            AppState.currentRoom = this.currentRoom;
            
            // 通过Socket加入房间
            if (AppState.socket) {
                AppState.socket.emit('join_room', { room_id: roomId });
            }
            
            // 切换到房间界面
            this.switchToRoom(this.currentRoom);
            
            // 加载房间消息
            if (window.chatManager) {
                await window.chatManager.loadRoomMessages(roomId);
            }
            
            // 更新房间列表显示
            this.renderRoomsList();
            
            showNotification(`已加入房间: ${this.currentRoom.name}`, 'success');
            
        } catch (error) {
            console.error('加入房间失败:', error);
            
            // 如果是私有房间需要密码
            if (error.message.includes('密码')) {
                this.showRoomPasswordDialog(roomId);
            } else {
                showNotification(error.message || '加入房间失败', 'error');
            }
        }
    }
    
    // 离开当前房间
    async leaveCurrentRoom() {
        if (!this.currentRoom) return;
        
        try {
            // 通过Socket离开房间
            if (AppState.socket) {
                AppState.socket.emit('leave_room', { room_id: this.currentRoom.id });
            }
            
            // 调用API离开房间
            await RoomsAPI.leaveRoom(this.currentRoom.id);
            
        } catch (error) {
            console.error('离开房间失败:', error);
        }
    }
    
    // 切换到房间界面
    switchToRoom(room) {
        // 更新房间信息显示
        const roomNameEl = DOM.$('#current-room-name');
        const roomMembersEl = DOM.$('#room-members');
        const inputArea = DOM.$('#input-area');
        const messagesContainer = DOM.$('#messages-container');
        
        if (roomNameEl) {
            roomNameEl.textContent = room.name;
        }
        
        if (roomMembersEl) {
            roomMembersEl.textContent = `${room.member_count || 0} 人在线`;
        }
        
        // 显示输入区域
        if (inputArea) {
            DOM.show(inputArea);
        }
        
        // 清空欢迎消息
        if (messagesContainer) {
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }
        
        // 加载房间成员
        this.loadRoomMembers(room.id);
    }
    
    // 加载房间成员
    async loadRoomMembers(roomId) {
        try {
            const response = await RoomsAPI.getRoomMembers(roomId);
            const members = response.members || [];
            
            // 更新在线用户状态
            AppState.onlineUsers.clear();
            members.forEach(member => {
                if (member.is_online) {
                    AppState.onlineUsers.add(member.id);
                }
            });
            
            // 更新成员显示
            const roomMembersEl = DOM.$('#room-members');
            if (roomMembersEl) {
                const onlineCount = members.filter(m => m.is_online).length;
                roomMembersEl.textContent = `${onlineCount}/${members.length} 人在线`;
            }
            
        } catch (error) {
            console.error('加载房间成员失败:', error);
        }
    }
    
    // 显示房间密码对话框
    showRoomPasswordDialog(roomId) {
        const content = `
            <div class="form-group">
                <label class="form-label">房间密码</label>
                <input type="password" id="room-password" class="form-control" placeholder="请输入房间密码">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="window.roomsManager.joinRoomWithPassword('${roomId}')">加入</button>
            </div>
        `;
        
        window.app.showModal(content, '加入私有房间');
    }
    
    // 使用密码加入房间
    async joinRoomWithPassword(roomId) {
        const password = DOM.$('#room-password').value;
        if (!password) {
            showNotification('请输入房间密码', 'error');
            return;
        }
        
        window.app.closeModal();
        await this.joinRoom(roomId, password);
    }
    
    // 显示创建房间对话框
    showCreateRoomDialog() {
        const content = `
            <div class="form-group">
                <label class="form-label">房间名称</label>
                <input type="text" id="room-name" class="form-control" placeholder="输入房间名称" maxlength="50">
            </div>
            <div class="form-group">
                <label class="form-label">房间描述</label>
                <textarea id="room-description" class="form-textarea" placeholder="输入房间描述（可选）" maxlength="200"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">最大成员数</label>
                    <select id="room-max-members" class="form-select">
                        <option value="10">10人</option>
                        <option value="20">20人</option>
                        <option value="50" selected>50人</option>
                        <option value="100">100人</option>
                    </select>
                </div>
                <div class="form-group">
                    <div class="form-checkbox">
                        <input type="checkbox" id="room-is-private">
                        <label for="room-is-private">私有房间</label>
                    </div>
                </div>
            </div>
            <div id="room-password-group" class="form-group" style="display: none;">
                <label class="form-label">房间密码</label>
                <input type="password" id="room-password" class="form-control" placeholder="设置房间密码">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="window.app.closeModal()">取消</button>
                <button class="btn btn-primary" onclick="window.roomsManager.createRoom()">创建</button>
            </div>
        `;
        
        window.app.showModal(content, '创建房间');
        
        // 监听私有房间复选框变化
        setTimeout(() => {
            const privateCheckbox = DOM.$('#room-is-private');
            const passwordGroup = DOM.$('#room-password-group');
            
            privateCheckbox.addEventListener('change', () => {
                if (privateCheckbox.checked) {
                    DOM.show(passwordGroup);
                } else {
                    DOM.hide(passwordGroup);
                }
            });
        }, 100);
    }
    
    // 创建房间
    async createRoom() {
        const name = DOM.$('#room-name').value.trim();
        const description = DOM.$('#room-description').value.trim();
        const maxMembers = parseInt(DOM.$('#room-max-members').value);
        const isPrivate = DOM.$('#room-is-private').checked;
        const password = DOM.$('#room-password').value;
        
        if (!name) {
            showNotification('请输入房间名称', 'error');
            return;
        }
        
        if (isPrivate && !password) {
            showNotification('私有房间需要设置密码', 'error');
            return;
        }
        
        try {
            const response = await RoomsAPI.createRoom(name, description, isPrivate, maxMembers);
            
            window.app.closeModal();
            showNotification('房间创建成功', 'success');
            
            // 重新加载房间列表
            await this.loadRooms();
            
            // 自动加入新创建的房间
            if (response.room) {
                await this.joinRoom(response.room.id);
            }
            
        } catch (error) {
            console.error('创建房间失败:', error);
            showNotification(error.message || '创建房间失败', 'error');
        }
    }
    
    // 处理房间更新
    handleRoomUpdate(data) {
        const roomIndex = this.rooms.findIndex(room => room.id === data.room_id);
        if (roomIndex !== -1) {
            this.rooms[roomIndex] = { ...this.rooms[roomIndex], ...data.updates };
            this.renderRoomsList();
        }
    }
    
    // 处理用户加入
    handleUserJoined(data) {
        if (this.currentRoom && data.room_id === this.currentRoom.id) {
            AppState.onlineUsers.add(data.user_id);
            this.loadRoomMembers(data.room_id);
            
            if (window.chatManager) {
                window.chatManager.addSystemMessage(`${data.username} 加入了房间`);
            }
        }
    }
    
    // 处理用户离开
    handleUserLeft(data) {
        if (this.currentRoom && data.room_id === this.currentRoom.id) {
            AppState.onlineUsers.delete(data.user_id);
            this.loadRoomMembers(data.room_id);
            
            if (window.chatManager) {
                window.chatManager.addSystemMessage(`${data.username} 离开了房间`);
            }
        }
    }
    
    // 获取当前房间
    getCurrentRoom() {
        return this.currentRoom;
    }
}

// 全局函数
window.showCreateRoom = function() {
    window.roomsManager.showCreateRoomDialog();
};

window.showRoomSettings = function() {
    if (window.roomsManager.currentRoom) {
        // 实现房间设置功能
        showNotification('房间设置功能开发中', 'info');
    }
};

// 初始化房间管理器
document.addEventListener('DOMContentLoaded', () => {
    window.roomsManager = new RoomsManager();
});
