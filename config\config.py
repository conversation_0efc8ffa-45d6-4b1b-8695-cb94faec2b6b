import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///chat_platform.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'app/static/uploads'
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 5242880))  # 5MB
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    # 并发控制配置
    MAX_CONCURRENT_CONNECTIONS = 1000
    CONNECTION_POOL_SIZE = 20
    CONNECTION_POOL_OVERFLOW = 30
    
    # 消息配置
    MESSAGE_RECALL_TIME_LIMIT = 300  # 5分钟撤回时限
    MESSAGE_HISTORY_PAGE_SIZE = 10   # 历史消息分页大小
    
    # 文件上传配置
    ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    ALLOWED_AUDIO_EXTENSIONS = {'mp3', 'wav', 'ogg', 'm4a'}
    IMAGE_COMPRESSION_QUALITY = 85
    MAX_IMAGE_SIZE = (1920, 1080)
    
    # 邀请链接配置
    INVITE_LINK_EXPIRY_HOURS = 24
